@echo off
REM Quick Start Script for Generative Agents API Development Environment
REM This script starts the R2-based development environment

echo.
echo ========================================
echo  Starting Generative Agents API (R2)
echo ========================================
echo.

REM Check if Dock<PERSON> is running
docker ps >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop first.
    echo.
    pause
    exit /b 1
)

echo ✅ Docker is running
echo.

REM Check if .env file exists
if not exist ".env" (
    echo ❌ .env file not found. Please run setup first.
    echo    Run: .\scripts\dev-setup.ps1
    echo.
    pause
    exit /b 1
)

echo ✅ Environment file found
echo.

REM Start the development environment
echo 🚀 Starting R2-based development environment...
echo    - API will be available at: http://localhost:5000
echo    - API docs will be at: http://localhost:5000/docs
echo    - Health check at: http://localhost:5000/health
echo.
echo 💡 Press Ctrl+C to stop the environment
echo.

docker-compose up api

echo.
echo 🛑 Development environment stopped.
echo.
pause
