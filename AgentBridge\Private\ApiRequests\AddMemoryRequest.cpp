// Copyright Epic Games, Inc. All Rights Reserved.

#include "ApiRequests/AddMemoryRequest.h"
#include "Json.h"
#include "JsonUtilities.h"

UAddMemoryRequest::UAddMemoryRequest()
{
    MemoryData.Importance = 0.5f;
}

void UAddMemoryRequest::SetMemoryData(const FMemoryCreateRequest& InMemoryData)
{
    MemoryData = InMemoryData;
    // Ensure importance is clamped if set through the struct
    MemoryData.Importance = FMath::Clamp(MemoryData.Importance, 0.0f, 1.0f);
}

// Removed SetAgentId, SetMemoryContent, SetImportance as they are now handled by SetMemoryData

FString UAddMemoryRequest::GetVerb() const
{
    return TEXT("POST");
}

FString UAddMemoryRequest::GetEndpoint() const
{
    return TEXT("/api/memories/");
}

FString UAddMemoryRequest::GetRequestBody() const
{
    // Create the request body
    TSharedPtr<FJsonObject> RequestObj = MakeShareable(new FJsonObject);
    RequestObj->SetStringField("agent_id", MemoryData.AgentId);
    RequestObj->SetStringField("memory_content", MemoryData.MemoryContent);
    RequestObj->SetNumberField("importance", MemoryData.Importance);

    if (!MemoryData.Description.IsEmpty())
    {
        RequestObj->SetStringField("description", MemoryData.Description);
    }

    if (!MemoryData.CurrentTime.IsEmpty())
    {
        RequestObj->SetStringField("current_time", MemoryData.CurrentTime);
    }

    FString RequestBody;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestBody);
    FJsonSerializer::Serialize(RequestObj.ToSharedRef(), Writer);

    return RequestBody;
}

void UAddMemoryRequest::ProcessResponse(const FString& Response, bool bWasSuccessful, int32 StatusCode)
{
    if (bWasSuccessful && StatusCode == 200)
    {
        // Parse the response to get the memory data
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);

        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            // Extract memory data from response
            CreatedMemoryData.Id = JsonObject->GetStringField(TEXT("id"));
            CreatedMemoryData.AgentId = JsonObject->GetStringField(TEXT("agent_id"));
            CreatedMemoryData.Content = JsonObject->GetStringField(TEXT("content"));
            CreatedMemoryData.Importance = JsonObject->GetNumberField(TEXT("importance"));
            CreatedMemoryData.CreatedAt = JsonObject->GetStringField(TEXT("created_at"));

            if (JsonObject->HasField(TEXT("description")))
            {
                CreatedMemoryData.Description = JsonObject->GetStringField(TEXT("description"));
            }
        }
    }
}

FMemoryData UAddMemoryRequest::GetCreatedMemoryData() const
{
    return CreatedMemoryData;
}
