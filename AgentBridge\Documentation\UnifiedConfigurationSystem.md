# Unified Configuration Management System

## Overview

The Unified Configuration Management System consolidates all configuration loading functionality into a single, extensible subsystem while maintaining backward compatibility with existing Agent and Chapter configuration managers. This system reduces code duplication, improves maintainability, and provides a consistent interface for all configuration types.

## Architecture

### Core Components

1. **UConfigurationManager** - The unified subsystem that handles all configuration loading
2. **EConfigurationType** - Enumeration defining supported configuration types
3. **FConfigurationTypeInfo** - Metadata for each configuration type
4. **Wrapper Managers** - Type-specific managers that delegate to the unified system

### Configuration Types

- **Agent** - Agent creation and behavior configurations
- **Chapter** - Text adventure chapter configurations  
- **WorldInstance** - World engine instance configurations

## Key Features

### Unified Loading
- Single point of configuration loading logic
- Consistent HTTP request handling
- Unified local file operations
- Centralized caching system

### Type-Safe Access
- Template-based configuration retrieval
- Type-specific wrapper managers
- Compile-time type checking

### Extensibility
- Easy addition of new configuration types
- Configurable default paths per type
- Flexible delegate system

## Usage Examples

### Loading Agent Configurations
```cpp
// Via Agent Config Manager (wrapper)
UAgentConfigManager* AgentManager = GetGameInstance()->GetSubsystem<UAgentConfigManager>();
AgentManager->LoadAllConfigurationsRecursive();

// Via Unified System (direct)
UConfigurationManager* ConfigManager = GetGameInstance()->GetSubsystem<UConfigurationManager>();
ConfigManager->LoadAllConfigurationsRecursive(EConfigurationType::Agent);
```

### Retrieving Configurations
```cpp
// Get agent configuration
FAgentCreateRequest AgentConfig;
bool bSuccess = AgentManager->GetAgentCreateConfiguration("Agents/MyAgent.json", AgentConfig);

// Get chapter configuration
FChapterConfig ChapterConfig;
bool bSuccess = ChapterManager->GetChapterConfiguration("Chapters/Chapter1.json", ChapterConfig);

// Get world instance configuration
FWorldInstanceConfig WorldConfig;
bool bSuccess = WorldManager->GetWorldInstanceConfiguration("WorldInstances/TestWorld.json", WorldConfig);
```

### Adding New Configuration Types

1. **Add to EConfigurationType enum**:
```cpp
UENUM(BlueprintType)
enum class EConfigurationType : uint8
{
    Agent,
    Chapter,
    WorldInstance,
    NewType UMETA(DisplayName = "New Configuration Type")
};
```

2. **Update ConfigurationManager::InitializeConfigurationTypes()**:
```cpp
ConfigurationTypes.Add(EConfigurationType::NewType, 
    FConfigurationTypeInfo(EConfigurationType::NewType, TEXT("NewConfigs/"), TEXT("New Configuration")));
```

3. **Create wrapper manager** (optional but recommended for consistency)

## File Structure

```
AgentBridge/
├── Public/
│   ├── Config/
│   │   ├── ConfigurationManager.h          // Unified system
│   │   ├── AgentConfigManager.h            // Agent wrapper
│   │   ├── ChapterConfigManager.h          // Chapter wrapper
│   │   └── WorldInstanceConfigManager.h    // World instance wrapper
│   └── Models/
│       ├── ConfigurationModels.h          // Core types and enums
│       ├── AgentModels.h                   // Agent-specific models
│       └── TextAdventureModels.h           // Chapter-specific models
└── Private/
    └── Config/
        ├── ConfigurationManager.cpp        // Unified implementation
        ├── AgentConfigManager.cpp          // Agent wrapper implementation
        ├── ChapterConfigManager.cpp        // Chapter wrapper implementation
        └── WorldInstanceConfigManager.cpp  // World instance wrapper implementation
```

## Configuration Paths

Default paths for each configuration type:
- **Agent**: `Agents/`
- **Chapter**: `Chapters/`
- **WorldInstance**: `WorldInstances/`

These can be overridden in the `UAgentBridgeConfigSettings` or by passing custom paths to loading functions.

## Benefits

### Reduced Code Duplication
- Single implementation of HTTP request handling
- Shared local file operations
- Common JSON parsing logic
- Unified error handling

### Improved Maintainability
- Changes to core loading logic affect all configuration types
- Consistent behavior across all managers
- Centralized logging and debugging

### Better Architecture
- Clear separation of concerns
- Type-safe configuration access
- Extensible design for future configuration types
- Consistent API patterns

## Migration Notes

Existing code using `UAgentConfigManager` and `UChapterConfigManager` will continue to work without changes. The wrapper managers provide the same interface while delegating to the unified system internally.

## Future Enhancements

- Configuration validation system
- Configuration versioning support
- Hot-reloading of configurations
- Configuration dependency management
- Performance optimizations for large configuration sets
