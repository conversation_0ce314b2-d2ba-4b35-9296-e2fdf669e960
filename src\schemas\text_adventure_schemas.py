"""
Pydantic schemas for Text Adventure data validation and serialization.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


class ChapterCreate(BaseModel):
    """Schema for creating a chapter."""
    chapter_id: str = Field(..., description="Unique identifier for the chapter")
    title: str = Field(..., description="Chapter title")
    system_prompt: str = Field(..., description="System prompt that determines narration style")
    initial_scene: str = Field(..., description="Initial scene description for the player")
    objectives: List[str] = Field(default_factory=list, description="Chapter objectives")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional chapter metadata")


class NarratorEngineCreate(BaseModel):
    """Schema for creating a new narrator engine."""
    narrator_id: str = Field(..., description="Unique identifier for the narrator")
    verbose: bool = Field(default=False, description="Enable verbose logging")


class UserActionRequest(BaseModel):
    """Request to process a user action in the text adventure."""
    user_action: str = Field(..., description="The user's intended action")
    user_location: str = Field(..., description="User's current location")
    agent_ids: List[str] = Field(..., description="List of all agent IDs in the world")


class AgentReaction(BaseModel):
    """A single agent's reaction to an observed action."""
    agent_id: str = Field(..., description="Agent identifier")
    agent_name: str = Field(..., description="Agent name")
    observation: str = Field(..., description="What the agent observed")
    action: str = Field(..., description="The agent's reaction")
    is_dialogue: bool = Field(..., description="Whether the reaction is dialogue")
    delay_seconds: int = Field(default=0, description="Delay in seconds before the agent noticed")


class ActionEvaluation(BaseModel):
    """Result of evaluating an action's feasibility."""
    is_possible: bool = Field(..., description="Whether the action is possible")
    reason: str = Field(..., description="Explanation for the evaluation")


class ChapterInfo(BaseModel):
    """Information about the current chapter."""
    chapter_id: str = Field(..., description="Chapter identifier")
    title: str = Field(..., description="Chapter title")
    turn: int = Field(..., description="Current turn in the chapter")


class TextAdventureResponse(BaseModel):
    """Response from processing a user action."""
    narration: str = Field(..., description="Narrative text describing the outcome")
    action_evaluation: ActionEvaluation = Field(..., description="Feasibility evaluation")
    observing_agents: int = Field(..., description="Number of agents who observed the action")
    agent_reactions: List[AgentReaction] = Field(..., description="Agent reactions")
    chapter_info: ChapterInfo = Field(..., description="Current chapter information")
    world_turn: int = Field(..., description="Current world turn count")
    timestamp: str = Field(..., description="ISO timestamp")


class NarratorState(BaseModel):
    """Current state of a narrator engine."""
    narrator_id: str = Field(..., description="Narrator identifier")
    chapters: Dict[str, Dict[str, Any]] = Field(..., description="All chapters")
    current_chapter: Optional[Dict[str, Any]] = Field(None, description="Current active chapter")
    narrative_history_length: int = Field(..., description="Length of narrative history")


class NarrativeHistoryEntry(BaseModel):
    """An entry in the narrative history."""
    type: str = Field(..., description="Type of entry (chapter_start, narration)")
    chapter_id: str = Field(..., description="Associated chapter ID")
    timestamp: str = Field(..., description="ISO timestamp")
    content: str = Field(..., description="Content of the entry")
    turn: Optional[int] = Field(None, description="Turn number if applicable")
    user_action: Optional[str] = Field(None, description="User action if applicable")


class StartChapterRequest(BaseModel):
    """Request to start a chapter."""
    chapter_id: str = Field(..., description="Chapter ID to start")


class StartChapterResponse(BaseModel):
    """Response from starting a chapter."""
    success: bool = Field(..., description="Whether the chapter started successfully")
    initial_scene: Optional[str] = Field(None, description="Initial scene description")
    error: Optional[str] = Field(None, description="Error message if failed")


class TextAdventureSetupRequest(BaseModel):
    """Request to set up a complete text adventure."""
    narrator_id: str = Field(..., description="Narrator identifier")
    world_id: str = Field(..., description="World identifier")
    chapter: ChapterCreate = Field(..., description="Initial chapter to create")
    world_rules: List[str] = Field(default_factory=list, description="World rules to add")
    agent_ids: List[str] = Field(default_factory=list, description="Agent IDs to include")


class TextAdventureSetupResponse(BaseModel):
    """Response from setting up a text adventure."""
    success: bool = Field(..., description="Whether setup was successful")
    narrator_id: str = Field(..., description="Narrator identifier")
    world_id: str = Field(..., description="World identifier")
    initial_scene: Optional[str] = Field(None, description="Initial scene if chapter was started")
    error: Optional[str] = Field(None, description="Error message if failed")
