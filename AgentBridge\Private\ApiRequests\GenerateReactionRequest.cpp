// Copyright Epic Games, Inc. All Rights Reserved.

#include "ApiRequests/GenerateReactionRequest.h"
#include "Json.h"
#include "JsonUtilities.h"

UGenerateReactionRequest::UGenerateReactionRequest()
{
}

void UGenerateReactionRequest::SetRequestData(const FString& InAgentId, const FAgentReactionRequest& InReactionRequestData)
{
    AgentId = InAgentId;
    ReactionRequest = InReactionRequestData;
}

// Removed SetAgentId, SetObservation, SetCurrentTime

FString UGenerateReactionRequest::GetVerb() const
{
    return TEXT("POST");
}

FString UGenerateReactionRequest::GetEndpoint() const
{
    return FString::Printf(TEXT("/api/agents/%s/react"), *AgentId);
}

FString UGenerateReactionRequest::GetRequestBody() const
{
    // Create the request body
    TSharedPtr<FJsonObject> RequestObj = MakeShareable(new FJsonObject);
    RequestObj->SetStringField("observation", ReactionRequest.Observation);
    
    if (!ReactionRequest.CurrentTime.IsEmpty())
    {
        RequestObj->SetStringField("current_time", ReactionRequest.CurrentTime);
    }
    
    FString RequestBody;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestBody);
    FJsonSerializer::Serialize(RequestObj.ToSharedRef(), Writer);
    
    return RequestBody;
}

void UGenerateReactionRequest::ProcessResponse(const FString& Response, bool bWasSuccessful, int32 StatusCode)
{
    if (bWasSuccessful && StatusCode == 200)
    {
        // Parse the response to get the reaction data
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);
        
        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            // Extract reaction data from response
            ReactionResponse.bIsDialogue = JsonObject->GetBoolField(TEXT("is_dialogue"));
            ReactionResponse.Reaction = JsonObject->GetStringField(TEXT("reaction"));
        }
    }
}

FAgentReactionResponse UGenerateReactionRequest::GetReactionResponse() const
{
    return ReactionResponse;
}
