# Enhanced Text Adventure System Guide

## Overview

The enhanced text adventure system provides a complete framework for creating agent-driven interactive narratives. The system integrates multiple components to create immersive, dynamic storytelling experiences where AI agents react intelligently to user actions.

## System Architecture

### Core Components

1. **NarratorEngine** - Manages narrative generation and chapter progression
2. **WorldEngine** - Handles world rules, action evaluation, and agent observation
3. **GenerativeAgents** - AI-driven NPCs that react to observations
4. **Chapter System** - Organizes adventures into manageable story segments

### Game Loop Flow

```
User Action → Feasibility Evaluation → Find Observing Agents → Agent Reactions → Narrative Generation
```

## Key Features

### 1. Action Feasibility Evaluation
- Uses world rules to determine if user actions are possible
- Returns boolean result with detailed reasoning
- Considers location, context, and established world constraints

### 2. Chronological Agent Observation
- Determines which agents would observe user actions based on:
  - Physical proximity
  - Line of sight
  - Nature of the action (loud vs quiet, visible vs hidden)
  - Agent alertness levels
- Returns agents in chronological order of when they would notice

### 3. Intelligent Agent Reactions
- Agents generate contextual reactions based on their:
  - Personality traits
  - Current status and location
  - Memory of past events
  - Relationship to the observed action
- Reactions can be dialogue or actions
- Memory system tracks observations and reactions

### 4. Rich Narrative Generation
- Combines user actions, agent reactions, and world state
- Maintains narrative consistency and style
- Incorporates chapter objectives and recent history
- Provides atmospheric descriptions and clear next steps

## API Endpoints

### Setup Endpoints

#### `POST /api/narrator/setup-text-adventure`
Sets up a complete text adventure with narrator, world, and initial chapter.

**Request:**
```json
{
  "narrator_id": "adventure-1",
  "world_id": "fantasy-world",
  "chapter": {
    "chapter_id": "chapter-1",
    "title": "The Beginning",
    "system_prompt": "Narrative style instructions...",
    "initial_scene": "Opening scene description...",
    "objectives": ["Goal 1", "Goal 2"]
  },
  "world_rules": ["Rule 1", "Rule 2"],
  "agent_ids": ["npc-1", "npc-2"]
}
```

### Game Loop Endpoint

#### `POST /api/narrator/{narrator_id}/process-action?world_id={world_id}`
Main game loop endpoint that processes user actions.

**Request:**
```json
{
  "user_action": "I walk into the tavern",
  "user_location": "Tavern entrance",
  "agent_ids": ["bartender", "patron-1", "patron-2"]
}
```

**Response:**
```json
{
  "narration": "Rich narrative description...",
  "action_evaluation": {
    "is_possible": true,
    "reason": "Action is physically possible and follows world rules"
  },
  "observing_agents": 2,
  "agent_reactions": [
    {
      "agent_id": "bartender",
      "agent_name": "Tom",
      "observation": "A new customer enters",
      "action": "Tom looks up and nods in greeting",
      "is_dialogue": false,
      "delay_seconds": 0
    }
  ],
  "chapter_info": {
    "chapter_id": "chapter-1",
    "title": "The Beginning",
    "turn": 3
  },
  "world_turn": 5,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Usage Examples

### 1. Basic Setup

```python
from services import narrator_service
from schemas.text_adventure_schemas import TextAdventureSetupRequest, ChapterCreate

# Create chapter
chapter = ChapterCreate(
    chapter_id="intro",
    title="The Mysterious Forest",
    system_prompt="You are a fantasy narrator...",
    initial_scene="You stand at the edge of a dark forest...",
    objectives=["Find the hidden village", "Solve the mystery"]
)

# Setup adventure
setup_request = TextAdventureSetupRequest(
    narrator_id="my-adventure",
    world_id="fantasy-world",
    chapter=chapter,
    world_rules=["Magic exists", "Monsters roam at night"],
    agent_ids=["guide", "merchant"]
)
```

### 2. Processing User Actions

```python
# Process user action
result = narrator_service.process_user_action(
    narrator_id="my-adventure",
    world_id="fantasy-world",
    user_action="I light a torch and enter the forest",
    user_location="Forest edge",
    agent_ids=["guide", "merchant"],
    user_id="player-1"
)

print(f"Narration: {result['narration']}")
print(f"Agents reacted: {len(result['agent_reactions'])}")
```

## Configuration

### Chapter System Prompts
Define the narrative style and tone:
- **Fantasy Adventure**: Descriptive, atmospheric, heroic
- **Mystery**: Suspenseful, detail-focused, investigative
- **Horror**: Tense, ominous, psychological
- **Comedy**: Light-hearted, humorous, exaggerated

### World Rules
Establish consistent world logic:
- Physical laws and limitations
- Magic system rules
- Social conventions
- Environmental constraints

### Agent Personalities
Create memorable NPCs with:
- Clear personality traits
- Specific motivations
- Unique speech patterns
- Defined relationships

## Best Practices

### 1. Chapter Design
- Keep chapters focused on 1-3 main objectives
- Provide clear initial scenes that set the mood
- Write system prompts that define narrative voice
- Plan for 10-20 turns per chapter

### 2. World Building
- Establish consistent rules early
- Consider how actions affect the world
- Plan for unexpected player creativity
- Balance realism with narrative needs

### 3. Agent Creation
- Give agents clear roles and motivations
- Vary personality types for interesting dynamics
- Consider how agents relate to each other
- Plan for both helpful and antagonistic NPCs

### 4. User Experience
- Provide clear feedback on action feasibility
- Ensure agent reactions feel natural
- Maintain narrative momentum
- Leave room for player agency

## Testing

Use the provided test script to verify system functionality:

```bash
python test_text_adventure.py
```

This will run through a complete adventure scenario and validate all components.

## Integration with Unreal Engine

The system provides C++ structs for Unreal Engine integration:
- `FTextAdventureSetupRequest`
- `FUserActionRequest`
- `FTextAdventureResponse`
- `FAgentReaction`
- `FActionEvaluation`

See `AgentBridge/Public/Models/TextAdventureModels.h` for complete definitions.
