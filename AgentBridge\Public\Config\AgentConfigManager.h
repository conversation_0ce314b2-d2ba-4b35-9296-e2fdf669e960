// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Models/AgentModels.h"
#include "Models/ConfigurationModels.h"
#include "AgentConfigManager.generated.h"

// Forward declarations
class UConfigurationManager;

// Legacy delegates for backward compatibility
DECLARE_DYNAMIC_DELEGATE_FourParams(FFileListResponseDelegate, bool, bSuccess, const FString&, Path, const TArray<FString>&, FileNames, const TArray<FString>&, FolderNames);
DECLARE_DYNAMIC_DELEGATE_ThreeParams(FFileContentResponseDelegate, bool, bSuccess, const FString&, FilePath, const FString&, JsonContent);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAllConfigsRequestCompleted, bool, bOverallSuccess);

/**
 * Agent Configuration Manager - wrapper around the unified Configuration Manager
 * Provides agent-specific configuration loading and management functionality.
 */
UCLASS()
class AGENTBRIDGE_API UAgentConfigManager : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    //~ Begin USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    //~ End USubsystem interface

    /**
     * Loads all agent configurations starting from the default or specified path.
     * @param OptionalPathInRepo Override the default root path from settings.
     * @param MaxRecursionDepth How many levels of subfolders to explore. 0 means only the given path.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Configuration")
    void LoadAllConfigurationsRecursive(const FString& OptionalPathInRepo = TEXT(""), int32 MaxRecursionDepth = 1);

    /**
     * Gets a list of files and folders at a specific path within the GitHub repository.
     * @param PathInRepo The path within the repository to list.
     * @param CompletionDelegate Delegate to call when the list operation completes.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Configuration")
    void GetFileList(const FString& PathInRepo, FFileListResponseDelegate CompletionDelegate);

    /**
     * Fetches and caches a single agent configuration file.
     * @param FilePathInRepo The full path to the configuration file within the repository.
     * @param CompletionDelegate Delegate to call when the fetch operation completes.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Configuration")
    void FetchAndCacheConfig(const FString& FilePathInRepo, FFileContentResponseDelegate CompletionDelegate);

    /**
     * Gets a parsed agent configuration by file path.
     * @param ConfigFilePathInRepo The path to the agent configuration file.
     * @param OutStruct The structure to populate with configuration data.
     * @return True if the configuration was found and successfully parsed.
     */
    template<typename TStructType>
    bool GetConfiguration(const FString& ConfigFilePathInRepo, TStructType& OutStruct) const;

    /**
     * Gets a specific agent configuration by file path.
     * @param ConfigFilePathInRepo The path to the agent configuration file within the repository.
     * @param OutConfig The FAgentCreateRequest structure to populate.
     * @return True if the configuration was found and successfully parsed.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Configuration", meta=(DisplayName="Get Agent Config By Path"))
    bool GetAgentCreateConfiguration(const FString& ConfigFilePathInRepo, FAgentCreateRequest& OutConfig) const;

    /**
     * Gets the raw agent configuration data cache map.
     * @return A const reference to the internal map of agent configuration data.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Configuration", meta=(DisplayName="Get Raw Config Cache"))
    const TMap<FString, FString>& GetRawConfigCache() const;

    /** Delegate broadcast when LoadAllConfigurationsRecursive completes. */
    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|Configuration")
    FOnAllConfigsRequestCompleted OnAllConfigsLoaded;

private:
    // Delegate handlers for unified system callbacks
    UFUNCTION()
    void OnUnifiedConfigsLoaded(EConfigurationType ConfigType, bool bSuccess);

    // Get reference to the unified configuration manager
    UConfigurationManager* GetConfigurationManager() const;
};

// Template implementation for GetConfiguration
template<typename TStructType>
bool UAgentConfigManager::GetConfiguration(const FString& ConfigFilePathInRepo, TStructType& OutStruct) const
{
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (ConfigManager)
    {
        return ConfigManager->GetConfiguration(EConfigurationType::Agent, ConfigFilePathInRepo, OutStruct);
    }
    return false;
}
