// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "ConfigurationModels.generated.h"

/**
 * Enumeration of supported configuration types
 */
UENUM(BlueprintType)
enum class EConfigurationType : uint8
{
    Agent       UMETA(DisplayName = "Agent Configuration"),
    Chapter     UMETA(DisplayName = "Chapter Configuration"),
    WorldInstance UMETA(DisplayName = "World Instance Configuration")
};

/**
 * Configuration type information
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FConfigurationTypeInfo
{
    GENERATED_BODY()

    /** The configuration type */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Configuration")
    EConfigurationType Type;

    /** Default path in repository for this configuration type */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Configuration")
    FString DefaultPath;

    /** File extension filter (e.g., "*.json") */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Configuration")
    FString FileExtension;

    /** Display name for this configuration type */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Configuration")
    FString DisplayName;

    FConfigurationTypeInfo()
    {
        Type = EConfigurationType::Agent;
        DefaultPath = TEXT("");
        FileExtension = TEXT("*.json");
        DisplayName = TEXT("");
    }

    FConfigurationTypeInfo(EConfigurationType InType, const FString& InDefaultPath, const FString& InDisplayName)
        : Type(InType)
        , DefaultPath(InDefaultPath)
        , FileExtension(TEXT("*.json"))
        , DisplayName(InDisplayName)
    {
    }
};

/**
 * World instance configuration for world engines
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FWorldInstanceConfig
{
    GENERATED_BODY()

    /** Unique identifier for the world instance */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldInstance")
    FString WorldId;

    /** Display name for the world instance */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldInstance")
    FString DisplayName;

    /** Description of the world instance */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldInstance")
    FString Description;

    /** Initial world rules */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldInstance")
    TArray<FString> InitialRules;

    /** Default agent IDs to include in this world */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldInstance")
    TArray<FString> DefaultAgentIds;

    /** Enable verbose logging for this world instance */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldInstance")
    bool bVerbose;

    /** Additional metadata for the world instance */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldInstance")
    TMap<FString, FString> Metadata;

    FWorldInstanceConfig()
    {
        bVerbose = false;
    }
};
