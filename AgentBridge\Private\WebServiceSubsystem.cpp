// Copyright Epic Games, Inc. All Rights Reserved.

#include "WebServiceSubsystem.h"
#include "HttpModule.h"
#include "Interfaces/IHttpResponse.h"
#include "ApiRequests/BaseApiRequest.h"
#include "Json.h"
#include "JsonUtilities.h"
#include "AgentBridgeSettings.h"
#include "AgentBridge.h"
#include "Misc/DateTime.h"

void UWebServiceSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);

    // Load settings using the standard approach
    const UAgentBridgeSettings* Settings = GetDefault<UAgentBridgeSettings>();
    if (Settings)
    {
        BaseUrl = Settings->ServiceUrl;
        bVerboseLogging = Settings->bEnableVerboseLogging;

        UE_LOG(LogAgentBridge, Error, TEXT("=== SETTINGS DEBUG ==="));
        UE_LOG(LogAgentBridge, Error, TEXT("Settings object: %p"), Settings);
        UE_LOG(LogAgentBridge, Error, TEXT("ServiceUrl from settings: '%s'"), *Settings->ServiceUrl);
        UE_LOG(LogAgentBridge, Error, TEXT("Expected from project settings: 'http://localhost:6000'"));
        UE_LOG(LogAgentBridge, Error, TEXT("Are they equal? %s"), Settings->ServiceUrl.Equals(TEXT("http://localhost:6000")) ? TEXT("YES") : TEXT("NO"));
        UE_LOG(LogAgentBridge, Error, TEXT("=== END DEBUG ==="));

        UE_LOG(LogAgentBridge, Log, TEXT("WebServiceSubsystem initialized with URL: %s, Verbose Logging: %s"),
            *BaseUrl, bVerboseLogging ? TEXT("Enabled") : TEXT("Disabled"));
    }
    else
    {
        // Fallback to default if settings not available
        BaseUrl = TEXT("http://localhost:5000");
        bVerboseLogging = false;
        UE_LOG(LogAgentBridge, Warning, TEXT("WebServiceSubsystem: Failed to load settings, using default service URL"));
    }
}

void UWebServiceSubsystem::Deinitialize()
{
    Super::Deinitialize();
}

void UWebServiceSubsystem::Login(const FString& Username, const FString& Password, FOnHttpRequestCompletedDelegate OnComplete)
{
    // Create the login request
    TSharedRef<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = FHttpModule::Get().CreateRequest();
    HttpRequest->SetVerb(TEXT("POST"));
    HttpRequest->SetURL(FString::Printf(TEXT("%s/auth/login"), *BaseUrl));
    HttpRequest->SetHeader(TEXT("Content-Type"), TEXT("application/json"));

    // Create the request body
    TSharedPtr<FJsonObject> RequestObj = MakeShareable(new FJsonObject);
    RequestObj->SetStringField(TEXT("username"), Username);
    RequestObj->SetStringField(TEXT("password"), Password);

    FString RequestBody;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestBody);
    FJsonSerializer::Serialize(RequestObj.ToSharedRef(), Writer);

    HttpRequest->SetContentAsString(RequestBody);

    // Create an internal delegate that will call both the OnLoginCompleted event and the provided callback
    FOnHttpRequestCompletedInternal InternalDelegate;
    InternalDelegate.BindLambda([this, OnComplete](bool bWasSuccessful, int32 StatusCode, const FString& Response)
    {
        // Call the provided callback
        if (OnComplete.IsBound())
        {
            OnComplete.Execute(bWasSuccessful, StatusCode, Response);
        }
    });

    // Execute the request
    ExecuteHttpRequest(HttpRequest, InternalDelegate);
}

void UWebServiceSubsystem::Logout()
{
    // Clear authentication data
    AuthToken.Empty();
    UserId.Empty();
}

bool UWebServiceSubsystem::IsLoggedIn() const
{
    return !AuthToken.IsEmpty();
}

FString UWebServiceSubsystem::GetCurrentUserToken() const
{
    return AuthToken;
}

FString UWebServiceSubsystem::GetCurrentUserId() const
{
    return UserId;
}

TSharedRef<IHttpRequest, ESPMode::ThreadSafe> UWebServiceSubsystem::ExecuteHttpRequest(TSharedRef<IHttpRequest, ESPMode::ThreadSafe> Request, FOnHttpRequestCompletedInternal OnComplete)
{
    // Add authentication token if available
    if (!AuthToken.IsEmpty())
    {
        Request->SetHeader(TEXT("Authorization"), FString::Printf(TEXT("Bearer %s"), *AuthToken));
    }

    // Add request ID for tracking in logs
    FGuid RequestId = FGuid::NewGuid();
    Request->SetHeader(TEXT("X-Request-ID"), RequestId.ToString());

    // Log the request
    UE_LOG(LogAgentBridge, Log, TEXT("[%s] Sending %s request to %s"),
        *RequestId.ToString(), *Request->GetVerb(), *Request->GetURL());

    // Log request headers and body if verbose logging is enabled
    if (bVerboseLogging)
    {
        // Log headers (except Authorization which contains sensitive data)
        FString HeadersLog;
        for (const FString& HeaderString : Request->GetAllHeaders())
        {
            FString KeyStr, ValueStr;
            if (HeaderString.Split(TEXT(": "), &KeyStr, &ValueStr))
            {
                if (!KeyStr.Equals(TEXT("Authorization"), ESearchCase::IgnoreCase))
                {
                    HeadersLog += FString::Printf(TEXT("%s: %s, "), *KeyStr, *ValueStr);
                }
                else
                {
                    HeadersLog += TEXT("Authorization: [REDACTED], ");
                }
            }
            else
            {
                HeadersLog += HeaderString + TEXT(", ");
            }
        }
        if (HeadersLog.Len() > 2) {
            HeadersLog.LeftChopInline(2);
        }
        UE_LOG(LogAgentBridge, Log, TEXT("[%s] Request Headers: %s"), *RequestId.ToString(), *HeadersLog);

        // Log request body (truncate if too long)
        FString Content;
        const TArray<uint8>& RequestContent = Request->GetContent();
        if (RequestContent.Num() > 0)
        {
            // Assuming UTF-8, adjust if another encoding is used
            FUTF8ToTCHAR Convert((const ANSICHAR*)RequestContent.GetData(), RequestContent.Num());
            Content = FString(Convert.Length(), Convert.Get());
        }
        if (!Content.IsEmpty())
        {
            // Truncate content if it's too long
            if (Content.Len() > 1000)
            {
                Content = Content.Left(1000) + TEXT("... [truncated]");
            }
            UE_LOG(LogAgentBridge, Log, TEXT("[%s] Request Body: %s"), *RequestId.ToString(), *Content);
        }
    }

    // Store the start time for timing the request
    const int64 StartTimeTicks = FDateTime::UtcNow().GetTicks();

    // Set up response handler - use weak pointer to prevent accessing destroyed subsystem
    TWeakObjectPtr<UWebServiceSubsystem> WeakThis(this);
    Request->OnProcessRequestComplete().BindLambda([WeakThis, OnComplete, StartTimeTicks](FHttpRequestPtr BoundRequest, FHttpResponsePtr BoundResponse, bool bWasSuccessfulLambda)
    {
        if (WeakThis.IsValid())
        {
            WeakThis.Get()->OnHttpResponseReceived(BoundRequest, BoundResponse, bWasSuccessfulLambda, OnComplete, StartTimeTicks);
        }
        else
        {
            UE_LOG(LogAgentBridge, Warning, TEXT("HTTP response received but WebServiceSubsystem was destroyed, skipping processing"));
            // Still execute the completion callback if valid
            if (OnComplete.IsBound())
            {
                int32 StatusCode = BoundResponse.IsValid() ? BoundResponse->GetResponseCode() : 0;
                FString ResponseStr = BoundResponse.IsValid() ? BoundResponse->GetContentAsString() : TEXT("");
                OnComplete.Execute(false, StatusCode, ResponseStr);
            }
        }
    });

    // Send the request
    Request->ProcessRequest();

    return Request;
}

void UWebServiceSubsystem::ExecuteHttpRequestBP(UObject* WorldContextObject, FString URL, FString Verb, FString ContentType, FString Body, FOnHttpRequestCompletedDelegate OnComplete)
{
    // Create the HTTP request
    TSharedRef<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = FHttpModule::Get().CreateRequest();
    HttpRequest->SetVerb(Verb);
    HttpRequest->SetURL(URL);
    HttpRequest->SetHeader(TEXT("Content-Type"), ContentType);

    if (!Body.IsEmpty())
    {
        HttpRequest->SetContentAsString(Body);
    }

    // Create an internal delegate that will call the provided callback
    FOnHttpRequestCompletedInternal InternalDelegate;
    InternalDelegate.BindLambda([OnComplete](bool bWasSuccessful, int32 StatusCode, const FString& Response)
    {
        // Call the provided callback
        if (OnComplete.IsBound())
        {
            OnComplete.Execute(bWasSuccessful, StatusCode, Response);
        }
    });

    // Execute the request
    ExecuteHttpRequest(HttpRequest, InternalDelegate);
}

bool UWebServiceSubsystem::ExecuteApiRequest(UBaseApiRequest* ApiRequest)
{
    if (!ApiRequest)
    {
        UE_LOG(LogTemp, Error, TEXT("UWebServiceSubsystem::ExecuteApiRequest: ApiRequest is null"));
        return false;
    }

    return ApiRequest->Execute(this);
}

FString UWebServiceSubsystem::GetBaseUrl() const
{
    return BaseUrl;
}

void UWebServiceSubsystem::SetBaseUrl(const FString& NewBaseUrl)
{
    BaseUrl = NewBaseUrl;
}

void UWebServiceSubsystem::OnHttpResponseReceived(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FOnHttpRequestCompletedInternal OnComplete, int64 StartTimeTicks)
{
    int32 StatusCode = Response.IsValid() ? Response->GetResponseCode() : 0;
    FString ResponseStr = Response.IsValid() ? Response->GetContentAsString() : TEXT("");

    // Get request ID from headers for log correlation
    FString RequestId = TEXT("Unknown");
    if (Request.IsValid())
    {
        const FString& RequestIdHeaderValue = Request->GetHeader(TEXT("X-Request-ID"));
        if (!RequestIdHeaderValue.IsEmpty())
        {
            RequestId = RequestIdHeaderValue;
        }
    }

    // Calculate request duration
    FString DurationStr = TEXT("Unknown");
    if (StartTimeTicks > 0)
    {
        int64 EndTime = FDateTime::UtcNow().GetTicks();
        float DurationMs = (EndTime - StartTimeTicks) / 10000.0f; // Convert ticks to milliseconds
        DurationStr = FString::Printf(TEXT("%.2f ms"), DurationMs);
    }

    // Log the response
    if (bWasSuccessful)
    {
        UE_LOG(LogAgentBridge, Log, TEXT("[%s] Response received: HTTP %d, Duration: %s"),
            *RequestId, StatusCode, *DurationStr);
    }
    else
    {
        UE_LOG(LogAgentBridge, Warning, TEXT("[%s] Request failed: HTTP %d, Duration: %s"),
            *RequestId, StatusCode, *DurationStr);
    }

    // Log response details if verbose logging is enabled
    if (bVerboseLogging)
    {
        // Log response headers
        if (Response.IsValid())
        {
            FString HeadersLog;
            for (const FString& HeaderString : Response->GetAllHeaders())
            {
                FString KeyStr, ValueStr;
                if (HeaderString.Split(TEXT(": "), &KeyStr, &ValueStr))
                {
                    HeadersLog += FString::Printf(TEXT("%s: %s, "), *KeyStr, *ValueStr);
                }
                else
                {
                    HeadersLog += HeaderString + TEXT(", ");
                }
            }
            if (HeadersLog.Len() > 2) {
                HeadersLog.LeftChopInline(2);
            }
            UE_LOG(LogAgentBridge, Log, TEXT("[%s] Response Headers: %s"), *RequestId, *HeadersLog);
        }

        // Log response body (truncate if too long)
        if (!ResponseStr.IsEmpty())
        {
            FString LogResponseStr = ResponseStr;
            if (LogResponseStr.Len() > 1000)
            {
                LogResponseStr = LogResponseStr.Left(1000) + TEXT("... [truncated]");
            }
            UE_LOG(LogAgentBridge, Log, TEXT("[%s] Response Body: %s"), *RequestId, *LogResponseStr);
        }
    }

    // If this was a login request and it was successful, extract the token
    if (bWasSuccessful && StatusCode == 200 && Request->GetURL().Contains(TEXT("/auth/login")))
    {
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(ResponseStr);

        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            // Extract token and user ID from response
            if (JsonObject->HasField(TEXT("token")))
            {
                AuthToken = JsonObject->GetStringField(TEXT("token"));
                UE_LOG(LogAgentBridge, Log, TEXT("[%s] Login successful, auth token received"), *RequestId);
            }

            if (JsonObject->HasField(TEXT("user_id")))
            {
                UserId = JsonObject->GetStringField(TEXT("user_id"));
                UE_LOG(LogAgentBridge, Log, TEXT("[%s] User ID received: %s"), *RequestId, *UserId);
            }
        }

        // Broadcast the login completion event
        if (OnLoginCompleted.IsBound())
        {
            OnLoginCompleted.Broadcast(bWasSuccessful, StatusCode, ResponseStr);
        }
    }

    // Execute the delegate
    if (OnComplete.IsBound())
    {
        OnComplete.Execute(bWasSuccessful, StatusCode, ResponseStr);
    }
}
