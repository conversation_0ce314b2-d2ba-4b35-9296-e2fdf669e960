// Copyright Epic Games, Inc. All Rights Reserved.

#include "WorldEngineSubsystem.h"
#include "WebServiceSubsystem.h"
#include "ApiRequests/CreateWorldEngineRequest.h"
#include "ApiRequests/ExecuteTurnRequest.h"
#include "AgentBridge.h"
#include "Engine/GameInstance.h"

void UWorldEngineSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);

    // Get the web service subsystem
    WebServiceSubsystem = GetGameInstance()->GetSubsystem<UWebServiceSubsystem>();
    if (!WebServiceSubsystem)
    {
        UE_LOG(LogAgentBridge, Error, TEXT("Failed to get WebServiceSubsystem"));
    }

    UE_LOG(LogAgentBridge, Log, TEXT("WorldEngineSubsystem initialized"));
}

void UWorldEngineSubsystem::Deinitialize()
{
    // Clean up any active requests
    if (ActiveCreateRequest)
    {
        ActiveCreateRequest->ConditionalBeginDestroy();
        ActiveCreateRequest = nullptr;
    }

    if (ActiveTurnRequest)
    {
        ActiveTurnRequest->ConditionalBeginDestroy();
        ActiveTurnRequest = nullptr;
    }

    Super::Deinitialize();
    UE_LOG(LogAgentBridge, Log, TEXT("WorldEngineSubsystem deinitialized"));
}

void UWorldEngineSubsystem::CreateWorldEngine(const FWorldEngineCreateRequest& WorldData)
{
    if (!WebServiceSubsystem)
    {
        UE_LOG(LogAgentBridge, Error, TEXT("WebServiceSubsystem not available"));
        OnWorldEngineCreated.Broadcast(false, FWorldEngineState());
        return;
    }

    // Clean up any previous request
    if (ActiveCreateRequest)
    {
        ActiveCreateRequest->ConditionalBeginDestroy();
    }

    // Create new request
    ActiveCreateRequest = NewObject<UCreateWorldEngineRequest>(this);
    ActiveCreateRequest->SetWorldData(WorldData);

    // Bind to completion
    ActiveCreateRequest->OnRequestCompleted.AddLambda([this](bool bSuccess, int32 StatusCode, const FString& Response)
    {
        HandleCreateWorldResponse(bSuccess, StatusCode, Response);
    });

    // Execute the request
    if (!ActiveCreateRequest->Execute(WebServiceSubsystem))
    {
        UE_LOG(LogAgentBridge, Error, TEXT("Failed to execute CreateWorldEngine request"));
        OnWorldEngineCreated.Broadcast(false, FWorldEngineState());
    }
}

void UWorldEngineSubsystem::ExecuteTurn(const FString& WorldId, const FTurnExecutionRequest& TurnData)
{
    if (!WebServiceSubsystem)
    {
        UE_LOG(LogAgentBridge, Error, TEXT("WebServiceSubsystem not available"));
        OnTurnExecuted.Broadcast(false, FTurnExecutionResponse());
        return;
    }

    if (WorldId.IsEmpty())
    {
        UE_LOG(LogAgentBridge, Error, TEXT("WorldId is empty"));
        OnTurnExecuted.Broadcast(false, FTurnExecutionResponse());
        return;
    }

    // Clean up any previous request
    if (ActiveTurnRequest)
    {
        ActiveTurnRequest->ConditionalBeginDestroy();
    }

    // Create new request
    ActiveTurnRequest = NewObject<UExecuteTurnRequest>(this);
    ActiveTurnRequest->SetWorldId(WorldId);
    ActiveTurnRequest->SetTurnData(TurnData);

    // Bind to completion
    ActiveTurnRequest->OnRequestCompleted.AddLambda([this](bool bSuccess, int32 StatusCode, const FString& Response)
    {
        HandleExecuteTurnResponse(bSuccess, StatusCode, Response);
    });

    // Execute the request
    if (!ActiveTurnRequest->Execute(WebServiceSubsystem))
    {
        UE_LOG(LogAgentBridge, Error, TEXT("Failed to execute ExecuteTurn request"));
        OnTurnExecuted.Broadcast(false, FTurnExecutionResponse());
    }
}

void UWorldEngineSubsystem::HandleCreateWorldResponse(bool bSuccess, int32 StatusCode, const FString& Response)
{
    if (bSuccess && ActiveCreateRequest)
    {
        CurrentWorldState = ActiveCreateRequest->GetCreatedWorldState();
        UE_LOG(LogAgentBridge, Log, TEXT("World engine created successfully: %s"), *CurrentWorldState.WorldId);
    }
    else
    {
        UE_LOG(LogAgentBridge, Error, TEXT("Failed to create world engine. Status: %d, Response: %s"), StatusCode, *Response);
    }

    OnWorldEngineCreated.Broadcast(bSuccess, CurrentWorldState);

    // Clean up request
    if (ActiveCreateRequest)
    {
        ActiveCreateRequest->ConditionalBeginDestroy();
        ActiveCreateRequest = nullptr;
    }
}

void UWorldEngineSubsystem::HandleExecuteTurnResponse(bool bSuccess, int32 StatusCode, const FString& Response)
{
    if (bSuccess && ActiveTurnRequest)
    {
        LastTurnResponse = ActiveTurnRequest->GetTurnResponse();
        UE_LOG(LogAgentBridge, Log, TEXT("Turn %d executed successfully for world: %s"),
            LastTurnResponse.Turn, *LastTurnResponse.WorldId);

        // Log agent results
        for (int32 i = 0; i < LastTurnResponse.AgentIds.Num(); i++)
        {
            const FString& AgentId = LastTurnResponse.AgentIds[i];
            const FAgentTurnResult& Result = LastTurnResponse.AgentResults[i];

            UE_LOG(LogAgentBridge, Verbose, TEXT("Agent %s: Action=%s, Success=%s, Possible=%s"),
                *AgentId,
                *Result.Action,
                Result.bSuccess ? TEXT("Yes") : TEXT("No"),
                Result.bIsPossible ? TEXT("Yes") : TEXT("No"));
        }
    }
    else
    {
        UE_LOG(LogAgentBridge, Error, TEXT("Failed to execute turn. Status: %d, Response: %s"), StatusCode, *Response);
    }

    OnTurnExecuted.Broadcast(bSuccess, LastTurnResponse);

    // Clean up request
    if (ActiveTurnRequest)
    {
        ActiveTurnRequest->ConditionalBeginDestroy();
        ActiveTurnRequest = nullptr;
    }
}
