version: '3.8'

services:
  # Main API service
  api:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "5000:5000"
    environment:
      # API Configuration
      DEBUG: "${DEBUG:-true}"
      LOG_LEVEL: "${LOG_LEVEL:-DEBUG}"
      BACKEND_API_KEY: "${BACKEND_API_KEY}"

      # OpenAI API Configuration
      OPENAI_API_KEY: "${OPENAI_API_KEY}"
      DEFAULT_MODEL_NAME: "${DEFAULT_MODEL_NAME:-gpt-4o}"
      DEFAULT_EMBEDDINGS_MODEL: "${DEFAULT_EMBEDDINGS_MODEL:-text-embedding-3-small}"

      # Memory Configuration
      MEMORY_IMPORTANCE_WEIGHT: "${MEMORY_IMPORTANCE_WEIGHT:-0.15}"
      REFLECTION_THRESHOLD: "${REFLECTION_THRESHOLD:-0.5}"
      MEMORY_K: "${MEMORY_K:-10}"

      # File Persistence Configuration (R2-based)
      ENABLE_PERSISTENCE: "${ENABLE_PERSISTENCE:-true}"
      DATA_DIR: "${DATA_DIR:-/tmp}"
      DEFAULT_USER_ID: "${DEFAULT_USER_ID:-default}"

      # R2 Bucket Configuration
      R2_ACCESS_KEY_ID: "${R2_ACCESS_KEY_ID}"
      R2_SECRET_ACCESS_KEY: "${R2_SECRET_ACCESS_KEY}"
      R2_BUCKET_NAME: "${R2_BUCKET_NAME}"
      R2_ENDPOINT_URL: "${R2_ENDPOINT_URL}"

      # GitHub Integration
      GITHUB_PAT: "${GITHUB_PAT}"
      GITHUB_REPO_OWNER: "${GITHUB_REPO_OWNER}"
      GITHUB_REPO_NAME: "${GITHUB_REPO_NAME}"
      GITHUB_BRANCH: "${GITHUB_BRANCH}"

      # Timeout Configuration
      GUNICORN_TIMEOUT: "300"
      GUNICORN_WORKERS: "1" # Single worker for development
      GUNICORN_MAX_REQUESTS: "1000"
      GUNICORN_LOG_LEVEL: "debug"
      REQUEST_TIMEOUT: "240"
      REQUEST_WARNING_THRESHOLD: "60"

      # Reflection Configuration
      ENABLE_REFLECTION: "${ENABLE_REFLECTION:-true}"

      # Debug Configuration
      LANGCHAIN_DEBUG_LOGGING: "${LANGCHAIN_DEBUG_LOGGING:-0}"

      # Development specific
      PYTHONPATH: "/app"
      PYTHONUNBUFFERED: "1"

    volumes:
      - .:/app
      - ./logs:/app/logs

    # Use development command
    command: python src/main.py

    # Enable hot reloading
    stdin_open: true
    tty: true

    networks:
      - agent-network

  # Optional: Production-like testing with Gunicorn
  api-prod:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "5001:5000"
    environment:
      # Same as api service but with production settings
      DEBUG: "false"
      LOG_LEVEL: "INFO"
      BACKEND_API_KEY: "${BACKEND_API_KEY}"

      # OpenAI API Configuration
      OPENAI_API_KEY: "${OPENAI_API_KEY}"
      DEFAULT_MODEL_NAME: "${DEFAULT_MODEL_NAME:-gpt-4o}"
      DEFAULT_EMBEDDINGS_MODEL: "${DEFAULT_EMBEDDINGS_MODEL:-text-embedding-3-small}"

      # Memory Configuration
      MEMORY_IMPORTANCE_WEIGHT: "${MEMORY_IMPORTANCE_WEIGHT:-0.15}"
      REFLECTION_THRESHOLD: "${REFLECTION_THRESHOLD:-0.5}"
      MEMORY_K: "${MEMORY_K:-10}"

      # File Persistence Configuration (R2-based)
      ENABLE_PERSISTENCE: "${ENABLE_PERSISTENCE:-true}"
      DATA_DIR: "${DATA_DIR:-/tmp}"
      DEFAULT_USER_ID: "${DEFAULT_USER_ID:-default}"

      # R2 Bucket Configuration
      R2_ACCESS_KEY_ID: "${R2_ACCESS_KEY_ID}"
      R2_SECRET_ACCESS_KEY: "${R2_SECRET_ACCESS_KEY}"
      R2_BUCKET_NAME: "${R2_BUCKET_NAME}"
      R2_ENDPOINT_URL: "${R2_ENDPOINT_URL}"

      # GitHub Integration
      GITHUB_PAT: "${GITHUB_PAT}"
      GITHUB_REPO_OWNER: "${GITHUB_REPO_OWNER}"
      GITHUB_REPO_NAME: "${GITHUB_REPO_NAME}"
      GITHUB_BRANCH: "${GITHUB_BRANCH}"

      # Timeout Configuration
      GUNICORN_TIMEOUT: "300"
      GUNICORN_WORKERS: "2"
      GUNICORN_MAX_REQUESTS: "1000"
      GUNICORN_LOG_LEVEL: "info"
      REQUEST_TIMEOUT: "240"
      REQUEST_WARNING_THRESHOLD: "60"

      # Reflection Configuration
      ENABLE_REFLECTION: "${ENABLE_REFLECTION:-true}"

      # Debug Configuration
      LANGCHAIN_DEBUG_LOGGING: "${LANGCHAIN_DEBUG_LOGGING:-0}"

    volumes:
      - .:/app
      - ./logs:/app/logs

    # Use production command
    command: gunicorn -c gunicorn.conf.py src.main:app

    networks:
      - agent-network

    profiles:
      - production

networks:
  agent-network:
    driver: bridge
