import logging
from typing import List, Optional

from fastapi import APIRouter, HTTPException, Query, Depends, Request # Added Request

from schemas.memory_schemas import (
    MemoryCreate,
    BulkMemoryCreate,
    MemoryQuery,
    ReflectionRequest,
    MemoryResponse,
    ReflectionResponse,
    Memory
)
from services import memory_service
from utils.llm_utils import validate_api_key
from utils.logging_utils import log_route_details # Added

router = APIRouter()
logger = logging.getLogger(__name__)

def verify_api_key():
    if not validate_api_key():
        raise HTTPException(
            status_code=500,
            detail="OpenAI API key is not configured. Please set the OPENAI_API_KEY environment variable."
        )
    return True

@router.post("/", status_code=201, summary="Add a memory to an agent")
@log_route_details(logger) # Added
async def add_memory(request: Request, memory: MemoryCreate, _: bool = Depends(verify_api_key)): # Added request
    # logger.info(f"Attempting to add memory for agent ID: {memory.agent_id}, content: '{memory.content[:50]}...', user_id: {memory.user_id}") # Covered by decorator
    """
    Add a new memory to a generative agent.

    - **agent_id**: ID of the agent to add memory to
    - **content**: Content of the memory to add
    - **current_time**: The time when the memory occurred (ISO format, optional)
    - **user_id**: ID of the user who owns the agent (optional)
    """
    # try: # Covered by decorator
    result = memory_service.add_memory(
        agent_id=memory.agent_id,
        content=memory.content,
        current_time=memory.current_time,
        user_id=memory.user_id
    )

    if result is None:
        # logger.warning(f"Agent with ID {memory.agent_id} not found for adding memory by user_id: {memory.user_id}.") # Covered by decorator
        raise HTTPException(status_code=404, detail=f"Agent with ID {memory.agent_id} not found")
    logger.info(f"Successfully added memory to agent ID: {memory.agent_id}.") # Keep specific success log
    return {"message": "Memory added successfully", "result": result}
    # except Exception as e: # Covered by decorator
    #     logger.error(f"Error adding memory: {e}", exc_info=True) # Covered by decorator
    #     raise HTTPException(status_code=500, detail="Failed to add memory") # Covered by decorator


@router.post("/bulk", status_code=201, summary="Add multiple memories to an agent")
@log_route_details(logger) # Added
async def add_memories(request: Request, memories: BulkMemoryCreate, _: bool = Depends(verify_api_key)): # Added request
    # logger.info(f"Attempting to add {len(memories.contents)} memories in bulk for agent ID: {memories.agent_id}, user_id: {memories.user_id}") # Covered by decorator
    """
    Add multiple memories to a generative agent.

    - **agent_id**: ID of the agent to add memories to
    - **contents**: List of memory contents to add
    - **current_time**: The time when the memories occurred (ISO format, optional)
    - **user_id**: ID of the user who owns the agent (optional)
    """
    # try: # Covered by decorator
    result = memory_service.add_memories(
        agent_id=memories.agent_id,
        contents=memories.contents,
        current_time=memories.current_time,
        user_id=memories.user_id
    )

    if result is None:
        # logger.warning(f"Agent with ID {memories.agent_id} not found for bulk adding memories by user_id: {memories.user_id}.") # Covered by decorator
        raise HTTPException(status_code=404, detail=f"Agent with ID {memories.agent_id} not found")
    logger.info(f"Successfully added {len(memories.contents)} memories in bulk to agent ID: {memories.agent_id}.") # Keep specific success log
    return {"message": f"Added {len(memories.contents)} memories successfully", "result": result}
    # except Exception as e: # Covered by decorator
    #     logger.error(f"Error adding memories in bulk: {e}", exc_info=True) # Covered by decorator
    #     raise HTTPException(status_code=500, detail="Failed to add memories in bulk") # Covered by decorator


@router.post("/query", response_model=MemoryResponse, summary="Query memories from an agent")
@log_route_details(logger) # Added
async def query_memories(request: Request, query: MemoryQuery, _: bool = Depends(verify_api_key)): # Added request
    # logger.info(f"Attempting to query memories for agent ID: {query.agent_id}, query: '{query.query[:50]}...', user_id: {query.user_id}") # Covered by decorator
    """
    Query memories from a generative agent based on a query string.

    - **agent_id**: ID of the agent to query memories from
    - **query**: Query to retrieve related memories
    - **current_time**: The current time (ISO format, optional)
    - **user_id**: ID of the user who owns the agent (optional)
    """
    # try: # Covered by decorator
    memories = memory_service.fetch_memories(
        agent_id=query.agent_id,
        query=query.query,
        current_time=query.current_time,
        user_id=query.user_id
    )

    if memories is None:
        # logger.warning(f"Agent with ID {query.agent_id} not found for querying memories by user_id: {query.user_id}.") # Covered by decorator
        raise HTTPException(status_code=404, detail=f"Agent with ID {query.agent_id} not found")

    memory_objects = [
        Memory(
            content=memory["content"],
            created_at=memory["created_at"],
            importance=memory["importance"]
        )
        for memory in memories
    ]
    logger.info(f"Successfully queried {len(memory_objects)} memories for agent ID: {query.agent_id}.") # Keep specific success log
    return MemoryResponse(memories=memory_objects)
    # except Exception as e: # Covered by decorator
    #     logger.error(f"Error querying memories: {e}", exc_info=True) # Covered by decorator
    #     raise HTTPException(status_code=500, detail="Failed to query memories") # Covered by decorator


@router.post("/reflect", response_model=ReflectionResponse, summary="Generate reflections for an agent")
@log_route_details(logger) # Added
async def generate_reflections(request: Request, reflection_req: ReflectionRequest, _: bool = Depends(verify_api_key)): # Added request
    # logger.info(f"Attempting to generate reflections for agent ID: {reflection_req.agent_id}, user_id: {reflection_req.user_id}") # Covered by decorator
    """
    Generate reflections for an agent based on recent memories.

    - **agent_id**: ID of the agent to generate reflections for
    - **current_time**: The current time (ISO format, optional)
    - **user_id**: ID of the user who owns the agent (optional)
    """
    # try: # Covered by decorator
    reflections = memory_service.generate_reflections(
        agent_id=reflection_req.agent_id,
        current_time=reflection_req.current_time,
        user_id=reflection_req.user_id
    )

    if reflections is None:
        # logger.warning(f"Agent with ID {reflection_req.agent_id} not found for generating reflections by user_id: {reflection_req.user_id}.") # Covered by decorator
        raise HTTPException(status_code=404, detail=f"Agent with ID {reflection_req.agent_id} not found")
    logger.info(f"Successfully generated {len(reflections) if reflections else 0} reflections for agent ID: {reflection_req.agent_id}.") # Keep specific success log
    return ReflectionResponse(reflections=reflections)
    # except Exception as e: # Covered by decorator
    #     logger.error(f"Error generating reflections: {e}", exc_info=True) # Covered by decorator
    #     raise HTTPException(status_code=500, detail="Failed to generate reflections") # Covered by decorator
