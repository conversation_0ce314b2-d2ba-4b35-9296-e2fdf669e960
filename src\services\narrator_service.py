"""
Service layer for Narrator Engine operations.

This module provides high-level functions for managing narrator engines,
executing text adventure game loops, and coordinating with world engines.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from core.narrator_engine import NarratorEngine, Chapter
from core.world_engine import WorldEngine
from services.world_engine_service import get_world_engine
from services.agent_service import get_agent
from utils.llm_utils import get_llm
from config import DEFAULT_USER_ID, ENABLE_PERSISTENCE

logger = logging.getLogger(__name__)

# In-memory storage for narrator engines
narrator_engines_store: Dict[str, NarratorEngine] = {}


def create_narrator_engine(
    narrator_id: str,
    verbose: bool = False,
    user_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create a new narrator engine instance.

    Args:
        narrator_id: Unique identifier for the narrator
        verbose: Enable verbose logging
        user_id: User who owns this narrator

    Returns:
        Dictionary containing narrator engine details
    """
    user_id = user_id or DEFAULT_USER_ID
    logger.info(f"Creating narrator engine '{narrator_id}' for user {user_id}")

    # Check if narrator already exists
    if narrator_id in narrator_engines_store:
        logger.warning(f"Narrator engine '{narrator_id}' already exists")
        return get_narrator_engine_info(narrator_id, user_id)

    # Create the narrator engine
    llm = get_llm()
    narrator_engine = NarratorEngine(
        narrator_id=narrator_id,
        llm=llm,
        verbose=verbose
    )

    # Store in memory
    narrator_engines_store[narrator_id] = narrator_engine

    # Prepare return data
    narrator_data = {
        "narrator_id": narrator_id,
        "user_id": user_id,
        "chapters": {},
        "current_chapter": None,
        "verbose": verbose
    }

    logger.info(f"Successfully created narrator engine '{narrator_id}'")
    return narrator_data


def get_narrator_engine(narrator_id: str, user_id: Optional[str] = None) -> Optional[NarratorEngine]:
    """
    Get a narrator engine instance.

    Args:
        narrator_id: Narrator identifier
        user_id: User ID (for future multi-user support)

    Returns:
        NarratorEngine instance or None if not found
    """
    narrator_engine = narrator_engines_store.get(narrator_id)
    if narrator_engine:
        return narrator_engine

    logger.warning(f"Narrator engine '{narrator_id}' not found")
    return None


def get_narrator_engine_info(narrator_id: str, user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Get information about a narrator engine.

    Args:
        narrator_id: Narrator identifier
        user_id: User ID

    Returns:
        Dictionary with narrator engine info or None
    """
    narrator_engine = get_narrator_engine(narrator_id, user_id)
    if not narrator_engine:
        return None

    chapters_info = {
        chapter_id: chapter.to_dict()
        for chapter_id, chapter in narrator_engine.chapters.items()
    }

    return {
        "narrator_id": narrator_id,
        "chapters": chapters_info,
        "current_chapter": narrator_engine.get_current_chapter_info(),
        "narrative_history_length": len(narrator_engine.narrative_history)
    }


def create_and_add_chapter(
    narrator_id: str,
    chapter_data: Dict[str, Any],
    user_id: Optional[str] = None
) -> bool:
    """
    Create and add a chapter to a narrator engine.

    Args:
        narrator_id: Narrator identifier
        chapter_data: Dictionary containing chapter information
        user_id: User ID

    Returns:
        True if successful
    """
    narrator_engine = get_narrator_engine(narrator_id, user_id)
    if not narrator_engine:
        logger.error(f"Narrator engine '{narrator_id}' not found")
        return False

    # Create chapter instance
    chapter = Chapter(
        chapter_id=chapter_data["chapter_id"],
        title=chapter_data["title"],
        system_prompt=chapter_data["system_prompt"],
        initial_scene=chapter_data["initial_scene"],
        objectives=chapter_data.get("objectives", []),
        metadata=chapter_data.get("metadata", {})
    )

    narrator_engine.add_chapter(chapter)
    logger.info(f"Added chapter '{chapter.title}' to narrator '{narrator_id}'")

    return True


def start_chapter(
    narrator_id: str,
    chapter_id: str,
    user_id: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """
    Start a chapter in the narrator engine.

    Args:
        narrator_id: Narrator identifier
        chapter_id: Chapter identifier
        user_id: User ID

    Returns:
        Dictionary with success status and initial scene, or None if error
    """
    narrator_engine = get_narrator_engine(narrator_id, user_id)
    if not narrator_engine:
        logger.error(f"Narrator engine '{narrator_id}' not found")
        return {"success": False, "error": "Narrator engine not found"}

    try:
        initial_scene = narrator_engine.start_chapter(chapter_id)
        return {
            "success": True,
            "initial_scene": initial_scene,
            "chapter_id": chapter_id,
            "narrator_id": narrator_id
        }
    except ValueError as e:
        logger.error(f"Error starting chapter: {e}")
        return {"success": False, "error": str(e)}


def process_user_action(
    narrator_id: str,
    world_id: str,
    user_action: str,
    user_location: str,
    agent_ids: List[str],
    user_id: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """
    Process a user action in the text adventure.

    This is the main game loop function that:
    1. Evaluates action feasibility
    2. Finds observing agents
    3. Triggers agent reactions
    4. Generates narrative response

    Args:
        narrator_id: Narrator identifier
        world_id: World identifier
        user_action: The user's action string
        user_location: User's current location
        agent_ids: List of all agent IDs in the world
        user_id: User ID

    Returns:
        Dictionary with narrative response and game state updates
    """
    user_id = user_id or DEFAULT_USER_ID
    logger.info(f"Processing user action for narrator '{narrator_id}', world '{world_id}'")

    # Get narrator and world engines
    narrator_engine = get_narrator_engine(narrator_id, user_id)
    if not narrator_engine:
        logger.error(f"Narrator engine '{narrator_id}' not found")
        return None

    world_engine = get_world_engine(world_id, user_id)
    if not world_engine:
        logger.error(f"World engine '{world_id}' not found")
        return None

    # Check if a chapter is active
    if not narrator_engine.current_chapter:
        logger.error("No chapter is currently active")
        return None

    # Get all agent data
    agents_data = []
    for agent_id in agent_ids:
        agent_data = get_agent(agent_id, user_id)
        if agent_data:
            agents_data.append(agent_data)

    try:
        # Step 1: Evaluate user action feasibility
        action_evaluation = world_engine.evaluate_user_action(user_action, user_location)
        logger.debug(f"Action evaluation: {action_evaluation}")

        # Step 2: Find agents who would observe the action
        observing_agents = world_engine.find_observing_agents(
            user_action,
            user_location,
            agents_data
        )
        logger.debug(f"Found {len(observing_agents)} observing agents")

        # Step 3: Trigger reactions for observing agents in chronological order
        agent_reactions = []

        # Sort observing agents by delay_seconds to ensure chronological processing
        sorted_observers = sorted(observing_agents, key=lambda x: x.get("delay_seconds", 0))

        for observer in sorted_observers:
            agent_id = observer["agent_id"]
            observation = observer["observation"]
            delay_seconds = observer.get("delay_seconds", 0)

            # Find the agent data
            agent_data = next((a for a in agents_data if a["id"] == agent_id), None)
            if not agent_data:
                logger.warning(f"Agent data not found for agent_id: {agent_id}")
                continue

            agent = agent_data["agent"]
            agent_name = agent_data["name"]

            # Generate agent reaction
            is_dialogue, reaction = agent.generate_reaction(
                observation=observation,
                now=datetime.now()
            )

            # Add observation to agent's memory
            agent.add_memory(observation)

            # Add reaction to agent's memory as well
            reaction_memory = f"I reacted to the observation by: {reaction}"
            agent.add_memory(reaction_memory)

            agent_reactions.append({
                "agent_id": agent_id,
                "agent_name": agent_name,
                "observation": observation,
                "action": reaction,
                "is_dialogue": is_dialogue,
                "delay_seconds": delay_seconds
            })

            logger.debug(f"Agent {agent_name} (delay: {delay_seconds}s) reacted: {reaction}")

        # Step 4: Get current world state with enhanced information
        agent_locations = {}
        agent_statuses = {}
        for agent_data in agents_data:
            agent_name = agent_data["name"]
            agent_locations[agent_name] = agent_data.get("location", "Unknown")
            agent_statuses[agent_name] = agent_data.get("status", "Unknown")

        world_state = {
            "turn_count": world_engine.turn_count,
            "world_id": world_id,
            "user_location": user_location,
            "user_action": user_action,
            "active_agents": [a["name"] for a in agents_data],
            "agent_locations": agent_locations,
            "agent_statuses": agent_statuses,
            "observing_agents_count": len(observing_agents),
            "action_feasible": action_evaluation.get("is_possible", True)
        }

        # Step 5: Generate narrative response
        narration = narrator_engine.generate_narration(
            user_action=user_action,
            agent_reactions=agent_reactions,
            world_state=world_state,
            action_evaluation=action_evaluation
        )

        # Step 6: Update world turn count
        world_engine.turn_count += 1

        # Prepare response
        response = {
            "narration": narration,
            "action_evaluation": action_evaluation,
            "observing_agents": len(observing_agents),
            "agent_reactions": agent_reactions,
            "chapter_info": {
                "chapter_id": narrator_engine.current_chapter.chapter_id,
                "title": narrator_engine.current_chapter.title,
                "turn": narrator_engine.current_chapter.turn_count
            },
            "world_turn": world_engine.turn_count,
            "timestamp": datetime.now().isoformat()
        }

        # Persist if enabled
        if ENABLE_PERSISTENCE:
            # Save agent states
            for agent_data in agents_data:
                from services import persistence_service
                persistence_service.save_agent(agent_data, user_id)

        logger.info(f"Successfully processed user action for turn {narrator_engine.current_chapter.turn_count}")
        return response

    except Exception as e:
        logger.error(f"Error processing user action: {e}", exc_info=True)
        return None


def get_narrative_history(
    narrator_id: str,
    user_id: Optional[str] = None,
    limit: Optional[int] = None
) -> Optional[List[Dict[str, Any]]]:
    """
    Get the narrative history for a narrator.

    Args:
        narrator_id: Narrator identifier
        user_id: User ID
        limit: Maximum number of entries to return

    Returns:
        List of narrative history entries or None
    """
    narrator_engine = get_narrator_engine(narrator_id, user_id)
    if not narrator_engine:
        return None

    history = narrator_engine.get_narrative_history()

    if limit:
        return history[-limit:]

    return history


def complete_current_chapter(
    narrator_id: str,
    user_id: Optional[str] = None
) -> bool:
    """
    Complete the current chapter.

    Args:
        narrator_id: Narrator identifier
        user_id: User ID

    Returns:
        True if successful
    """
    narrator_engine = get_narrator_engine(narrator_id, user_id)
    if not narrator_engine:
        return False

    narrator_engine.complete_current_chapter()
    return True


def list_narrator_engines(user_id: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    List all narrator engines for a user.

    Args:
        user_id: User ID

    Returns:
        List of narrator engine info dictionaries
    """
    user_id = user_id or DEFAULT_USER_ID
    logger.info(f"Listing narrator engines for user {user_id}")

    narrator_engines_list = []
    for narrator_id, narrator_engine in narrator_engines_store.items():
        info = get_narrator_engine_info(narrator_id, user_id)
        if info:
            narrator_engines_list.append(info)

    return narrator_engines_list


def delete_narrator_engine(narrator_id: str, user_id: Optional[str] = None) -> bool:
    """
    Delete a narrator engine.

    Args:
        narrator_id: Narrator identifier
        user_id: User ID

    Returns:
        True if deleted successfully
    """
    if narrator_id not in narrator_engines_store:
        logger.warning(f"Narrator engine '{narrator_id}' not found for deletion")
        return False

    del narrator_engines_store[narrator_id]
    logger.info(f"Deleted narrator engine '{narrator_id}'")

    return True
