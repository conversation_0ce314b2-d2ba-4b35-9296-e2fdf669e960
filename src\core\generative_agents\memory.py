import logging
import re
from datetime import datetime
from typing import Any, Dict, List, Optional

from langchain.chains import <PERSON><PERSON><PERSON><PERSON>
from langchain.retrievers import TimeWeightedVectorStoreRetriever
from langchain.schema import BaseMemory, Document
# Replace langchain's mock_now with our serializable version
from utils.datetime_utils import serializable_mock_now
from langchain_core.language_models import BaseLanguageModel
from config import DEFAULT_REFLECTION_THRESHOLD, ENABLE_REFLECTION # Added import
from langchain_core.prompts import PromptTemplate

logger = logging.getLogger(__name__)


class SafeTimeWeightedVectorStoreRetriever(TimeWeightedVectorStoreRetriever):
    """
    A safe wrapper around TimeWeightedVectorStoreRetriever that prevents
    IndexError issues when buffer indices are out of sync with memory_stream.
    """
    
    def get_salient_docs(self, query: str) -> dict:
        """
        Override get_salient_docs to handle IndexError gracefully.
        """
        max_retries = 2
        for attempt in range(max_retries):
            try:
                return super().get_salient_docs(query)
            except IndexError as e:
                logger.warning(f"SafeTimeWeightedVectorStoreRetriever: IndexError in get_salient_docs (attempt {attempt + 1}/{max_retries}) - {e}. Attempting to fix.")
                
                # Reset buffer indices and try to rebuild memory_stream if needed
                self._fix_buffer_indices()
                
                if attempt == max_retries - 1:  # Last attempt
                    logger.error(f"SafeTimeWeightedVectorStoreRetriever: Failed to fix IndexError after {max_retries} attempts. Returning empty dict.")
                    return {}
            except Exception as e:
                logger.error(f"SafeTimeWeightedVectorStoreRetriever: Unexpected error in get_salient_docs: {e}", exc_info=True)
                return {}
        
        return {}  # Fallback
    
    def _get_relevant_documents(self, query: str, *, run_manager=None) -> List[Document]:
        """
        Override _get_relevant_documents to handle IndexError gracefully.
        """
        max_retries = 2
        for attempt in range(max_retries):
            try:
                return super()._get_relevant_documents(query, run_manager=run_manager)
            except IndexError as e:
                logger.warning(f"SafeTimeWeightedVectorStoreRetriever: IndexError in _get_relevant_documents (attempt {attempt + 1}/{max_retries}) - {e}. Attempting to fix.")
                
                # Reset buffer indices and try to rebuild memory_stream if needed
                self._fix_buffer_indices()
                
                if attempt == max_retries - 1:  # Last attempt
                    logger.error(f"SafeTimeWeightedVectorStoreRetriever: Failed to fix IndexError after {max_retries} attempts. Returning empty list.")
                    return []
            except Exception as e:
                logger.error(f"SafeTimeWeightedVectorStoreRetriever: Unexpected error in _get_relevant_documents: {e}", exc_info=True)
                return []
        
        return []  # Fallback
    
    def _fix_buffer_indices(self):
        """
        Fix buffer indices by ensuring they match the current memory_stream indices.
        Also attempts to rebuild memory_stream from vector store if it's empty.
        """
        try:
            # First check if memory_stream exists and has content
            if not hasattr(self, 'memory_stream'):
                self.memory_stream = []
                logger.debug("SafeTimeWeightedVectorStoreRetriever: memory_stream attribute was missing, initialized as empty list.")
            
            # If memory_stream is empty, try to rebuild it from the vector store
            if not self.memory_stream and hasattr(self, 'vectorstore') and self.vectorstore is not None:
                logger.debug("SafeTimeWeightedVectorStoreRetriever: Attempting to rebuild memory_stream from vector store.")
                self._rebuild_memory_stream_from_vectorstore()
            
            # If we still don't have memory_stream content, nothing to fix
            if not self.memory_stream:
                logger.debug("SafeTimeWeightedVectorStoreRetriever: memory_stream is empty after rebuild attempt.")
                return
            
            # Reset buffer indices to match current memory_stream order
            indices_fixed = 0
            for i, doc in enumerate(self.memory_stream):
                if not hasattr(doc, 'metadata') or doc.metadata is None:
                    doc.metadata = {}
                
                old_buffer_idx = doc.metadata.get('buffer_idx', 'missing')
                doc.metadata['buffer_idx'] = i
                
                if old_buffer_idx != i:
                    indices_fixed += 1
            
            # Clear any cached buffer state
            if hasattr(self, '_buffer'):
                self._buffer = []
            if hasattr(self, 'buffer'):
                self.buffer = []
            
            logger.debug(f"SafeTimeWeightedVectorStoreRetriever: Fixed buffer indices for {len(self.memory_stream)} documents ({indices_fixed} indices were corrected).")
            
        except Exception as e:
            logger.error(f"SafeTimeWeightedVectorStoreRetriever: Error fixing buffer indices - {e}", exc_info=True)
    
    def _rebuild_memory_stream_from_vectorstore(self):
        """
        Attempt to rebuild memory_stream from the underlying vector store.
        Uses multiple methods similar to the persistence service.
        """
        rebuilt_docs = []
        
        try:
            vectorstore = self.vectorstore
            
            # Method 1: Direct docstore access
            if (hasattr(vectorstore, 'docstore') and vectorstore.docstore is not None and
                hasattr(vectorstore.docstore, '_store') and vectorstore.docstore._store is not None):
                
                logger.debug("SafeTimeWeightedVectorStoreRetriever: Rebuilding from docstore._store")
                docs_from_store = list(vectorstore.docstore._store.values())
                rebuilt_docs = [doc for doc in docs_from_store if isinstance(doc, Document)]
            
            # Method 2: Similarity search fallback
            elif hasattr(vectorstore, 'index') and vectorstore.index is not None:
                logger.debug("SafeTimeWeightedVectorStoreRetriever: Rebuilding using similarity search")
                num_vectors = getattr(vectorstore.index, 'ntotal', 0)
                
                if num_vectors > 0:
                    generic_queries = ["memories", "experiences", "thoughts", "observations"]
                    all_docs = []
                    
                    for query in generic_queries:
                        try:
                            docs = vectorstore.similarity_search(query, k=min(num_vectors, 50))
                            all_docs.extend(docs)
                        except Exception as search_e:
                            logger.debug(f"SafeTimeWeightedVectorStoreRetriever: Search failed for query '{query}': {search_e}")
                            continue
                    
                    # Remove duplicates
                    seen_content = set()
                    for doc in all_docs:
                        if hasattr(doc, 'page_content') and doc.page_content not in seen_content:
                            seen_content.add(doc.page_content)
                            rebuilt_docs.append(doc)
            
            # Method 3: Index to docstore mapping
            elif hasattr(vectorstore, 'index_to_docstore_id') and hasattr(vectorstore, 'docstore'):
                logger.debug("SafeTimeWeightedVectorStoreRetriever: Rebuilding using index_to_docstore_id mapping")
                
                for idx, doc_id in vectorstore.index_to_docstore_id.items():
                    try:
                        doc = vectorstore.docstore.search(doc_id)
                        if isinstance(doc, Document):
                            rebuilt_docs.append(doc)
                    except Exception as doc_e:
                        logger.debug(f"SafeTimeWeightedVectorStoreRetriever: Failed to get document {doc_id}: {doc_e}")
                        continue
            
            if rebuilt_docs:
                # Sort documents by creation time or buffer_idx
                def sort_key(doc):
                    if hasattr(doc, 'metadata') and doc.metadata:
                        if 'created_at' in doc.metadata:
                            created_at = doc.metadata['created_at']
                            if isinstance(created_at, datetime):
                                return created_at.timestamp()
                            elif isinstance(created_at, str):
                                try:
                                    return datetime.fromisoformat(created_at.replace('Z', '+00:00')).timestamp()
                                except:
                                    pass
                        if 'buffer_idx' in doc.metadata:
                            return doc.metadata.get('buffer_idx', float('inf'))
                    return hash(doc.page_content) if hasattr(doc, 'page_content') else 0
                
                self.memory_stream = sorted(rebuilt_docs, key=sort_key)
                logger.debug(f"SafeTimeWeightedVectorStoreRetriever: Successfully rebuilt memory_stream with {len(self.memory_stream)} documents")
            else:
                logger.debug("SafeTimeWeightedVectorStoreRetriever: Could not rebuild memory_stream - no documents found")
                
        except Exception as e:
            logger.error(f"SafeTimeWeightedVectorStoreRetriever: Error rebuilding memory_stream: {e}", exc_info=True)


class GenerativeAgentMemory(BaseMemory):
    """Memory for the generative agent."""

    llm: BaseLanguageModel
    """The core language model."""
    memory_retriever: SafeTimeWeightedVectorStoreRetriever
    """The retriever to fetch related memories."""
    verbose: bool = False
    reflection_threshold: Optional[float] = DEFAULT_REFLECTION_THRESHOLD
    """When aggregate_importance exceeds reflection_threshold, stop to reflect."""
    reflection_enabled: bool = ENABLE_REFLECTION
    """Whether reflection is enabled globally."""
    current_plan: List[str] = []
    """The current plan of the agent."""
    # A weight of 0.15 makes this less important than it
    # would be otherwise, relative to salience and time
    importance_weight: float = 0.15
    """How much weight to assign the memory importance."""
    aggregate_importance: float = 0.0  # : :meta private:
    """Track the sum of the 'importance' of recent memories.

    Triggers reflection when it reaches reflection_threshold."""

    max_tokens_limit: int = 1200  # : :meta private:
    # input keys
    queries_key: str = "queries"
    most_recent_memories_token_key: str = "recent_memories_token"
    add_memory_key: str = "add_memory"
    # output keys
    relevant_memories_key: str = "relevant_memories"
    relevant_memories_simple_key: str = "relevant_memories_simple"
    most_recent_memories_key: str = "most_recent_memories"
    now_key: str = "now"
    reflecting: bool = False

    def chain(self, prompt: PromptTemplate) -> LLMChain:
        return LLMChain(llm=self.llm, prompt=prompt, verbose=self.verbose)

    @staticmethod
    def _parse_list(text: str) -> List[str]:
        """Parse a newline-separated string into a list of strings."""
        lines = re.split(r"\n", text.strip())
        lines = [line for line in lines if line.strip()]  # remove empty lines
        return [re.sub(r"^\s*\d+\.\s*", "", line).strip() for line in lines]

    def _get_topics_of_reflection(self, last_k: int = 50) -> List[str]:
        """Return the 3 most salient high-level questions about recent observations."""
        prompt = PromptTemplate.from_template(
            "{observations}\n\n"
            "Given only the information above, what are the 3 most salient "
            "high-level questions we can answer about the subjects in the statements?\n"
            "Provide each question on a new line."
        )
        observations = self.memory_retriever.memory_stream[-last_k:]
        observation_str = "\n".join(
            [self._format_memory_detail(o) for o in observations]
        )
        result = self.chain(prompt).run(observations=observation_str)
        return self._parse_list(result)

    def _get_insights_on_topic(
        self, topic: str, now: Optional[datetime] = None
    ) -> List[str]:
        """Generate 'insights' on a topic of reflection, based on pertinent memories."""
        prompt = PromptTemplate.from_template(
            "Statements relevant to: '{topic}'\n"
            "---\n"
            "{related_statements}\n"
            "---\n"
            "What 5 high-level novel insights can you infer from the above statements "
            "that are relevant for answering the following question?\n"
            "Do not include any insights that are not relevant to the question.\n"
            "Do not repeat any insights that have already been made.\n\n"
            "Question: {topic}\n\n"
            "(example format: insight (because of 1, 5, 3))\n"
        )

        related_memories = self.fetch_memories(topic, now=now)
        related_statements = "\n".join(
            [
                self._format_memory_detail(memory, prefix=f"{i+1}. ")
                for i, memory in enumerate(related_memories)
            ]
        )
        result = self.chain(prompt).run(
            topic=topic, related_statements=related_statements
        )
        # TODO: Parse the connections between memories and insights
        return self._parse_list(result)

    def pause_to_reflect(self, now: Optional[datetime] = None) -> List[str]:
        """Reflect on recent observations and generate 'insights'."""
        if self.verbose:
            logger.info("Character is reflecting")
        new_insights = []
        topics = self._get_topics_of_reflection()
        for topic in topics:
            insights = self._get_insights_on_topic(topic, now=now)
            for insight in insights:
                self.add_memory(insight, now=now)
            new_insights.extend(insights)
        return new_insights

    def _score_memory_importance(self, memory_content: str) -> float:
        """Score the absolute importance of the given memory."""
        prompt = PromptTemplate.from_template(
            "On the scale of 1 to 10, where 1 is purely mundane"
            + " (e.g., brushing teeth, making bed) and 10 is"
            + " extremely poignant (e.g., a break up, college"
            + " acceptance), rate the likely poignancy of the"
            + " following piece of memory. Respond with a single integer."
            + "\nMemory: {memory_content}"
            + "\nRating: "
        )
        score = self.chain(prompt).run(memory_content=memory_content).strip()
        if self.verbose:
            logger.info(f"Importance score: {score}")
        match = re.search(r"^\D*(\d+)", score)
        if match:
            return (float(match.group(1)) / 10) * self.importance_weight
        else:
            return 0.0

    def _score_memories_importance(self, memory_content: str) -> List[float]:
        """Score the absolute importance of the given memory."""
        prompt = PromptTemplate.from_template(
            "On the scale of 1 to 10, where 1 is purely mundane"
            + " (e.g., brushing teeth, making bed) and 10 is"
            + " extremely poignant (e.g., a break up, college"
            + " acceptance), rate the likely poignancy of the"
            + " following piece of memory. Always answer with only a list of numbers."
            + " If just given one memory still respond in a list."
            + " Memories are separated by semi colans (;)"
            + "\nMemories: {memory_content}"
            + "\nRating: "
        )
        scores = self.chain(prompt).run(memory_content=memory_content).strip()

        if self.verbose:
            logger.info(f"Importance scores: {scores}")

        # Split into list of strings and convert to floats
        scores_list = [float(x) for x in scores.split(";")]

        return scores_list

    def add_memories(
        self, memory_content: str, now: Optional[datetime] = None
    ) -> List[str]:
        """Add an observations or memories to the agent's memory."""
        importance_scores = self._score_memories_importance(memory_content)

        self.aggregate_importance += max(importance_scores)
        memory_list = memory_content.split(";")
        documents = []

        for i in range(len(memory_list)):
            documents.append(
                Document(
                    page_content=memory_list[i],
                    metadata={"importance": importance_scores[i]},
                )
            )

        result = self.memory_retriever.add_documents(documents, current_time=now)

        # After an agent has processed a certain amount of memories (as measured by
        # aggregate importance), it is time to reflect on recent events to add
        # more synthesized memories to the agent's memory stream.
        if self.reflection_enabled:
            if (
                self.reflection_threshold is not None
                and self.aggregate_importance > self.reflection_threshold
                and not self.reflecting
            ):
                self.reflecting = True
                self.pause_to_reflect(now=now)
                # Hack to clear the importance from reflection
                self.aggregate_importance = 0.0
                self.reflecting = False
        return result

    def add_memory(
        self, memory_content: str, now: Optional[datetime] = None
    ) -> List[str]:
        """Add an observation or memory to the agent's memory."""
        importance_score = self._score_memory_importance(memory_content)
        self.aggregate_importance += importance_score
        document = Document(
            page_content=memory_content, metadata={"importance": importance_score}
        )
        result = self.memory_retriever.add_documents([document], current_time=now)

        # After an agent has processed a certain amount of memories (as measured by
        # aggregate importance), it is time to reflect on recent events to add
        # more synthesized memories to the agent's memory stream.
        if self.reflection_enabled:
            if (
                self.reflection_threshold is not None
                and self.aggregate_importance > self.reflection_threshold
                and not self.reflecting
            ):
                self.reflecting = True
                self.pause_to_reflect(now=now)
                # Hack to clear the importance from reflection
                self.aggregate_importance = 0.0
                self.reflecting = False
        return result

    def fetch_memories(
        self, observation: str, now: Optional[datetime] = None
    ) -> List[Document]:
        """Fetch related memories."""
        if now is not None:
            with serializable_mock_now(now) as mock_now:
                # The TimeWeightedVectorStoreRetriever uses datetime.now() internally
                # We can't modify datetime.now directly, so we pass the current_time parameter
                return self.memory_retriever.invoke(observation, current_time=now)
        else:
            return self.memory_retriever.invoke(observation)

    def format_memories_detail(self, relevant_memories: List[Document]) -> str:
        content = []
        for mem in relevant_memories:
            content.append(self._format_memory_detail(mem, prefix="- "))
        return "\n".join([f"{mem}" for mem in content])

    def _format_memory_detail(self, memory: Document, prefix: str = "") -> str:
        created_time = memory.metadata["created_at"].strftime("%B %d, %Y, %I:%M %p")
        return f"{prefix}[{created_time}] {memory.page_content.strip()}"

    def format_memories_simple(self, relevant_memories: List[Document]) -> str:
        return "; ".join([f"{mem.page_content}" for mem in relevant_memories])

    def _get_memories_until_limit(self, consumed_tokens: int) -> str:
        """Reduce the number of tokens in the documents."""
        result = []
        for doc in self.memory_retriever.memory_stream[::-1]:
            if consumed_tokens >= self.max_tokens_limit:
                break
            consumed_tokens += self.llm.get_num_tokens(doc.page_content)
            if consumed_tokens < self.max_tokens_limit:
                result.append(doc)
        return self.format_memories_simple(result)

    @property
    def memory_variables(self) -> List[str]:
        """Input keys this memory class will load dynamically."""
        return []

    def load_memory_variables(self, inputs: Dict[str, Any]) -> Dict[str, str]:
        """Return key-value pairs given the text input to the chain."""
        queries = inputs.get(self.queries_key)
        now = inputs.get(self.now_key)
        if queries is not None:
            # Collect all memories from multiple queries
            all_memories = []
            for query in queries:
                memories = self.fetch_memories(query, now=now)
                all_memories.extend(memories)
            
            # Deduplicate memories based on content to avoid duplicates
            seen_content = set()
            relevant_memories = []
            for mem in all_memories:
                content = mem.page_content.strip()
                if content not in seen_content:
                    seen_content.add(content)
                    relevant_memories.append(mem)
            
            return {
                self.relevant_memories_key: self.format_memories_detail(
                    relevant_memories
                ),
                self.relevant_memories_simple_key: self.format_memories_simple(
                    relevant_memories
                ),
            }

        most_recent_memories_token = inputs.get(self.most_recent_memories_token_key)
        if most_recent_memories_token is not None:
            return {
                self.most_recent_memories_key: self._get_memories_until_limit(
                    most_recent_memories_token
                )
            }
        return {}

    def save_context(self, inputs: Dict[str, Any], outputs: Dict[str, Any]) -> None:
        """Save the context of this model run to memory."""
        # TODO: fix the save memory key
        mem = outputs.get(self.add_memory_key)
        now = outputs.get(self.now_key)
        if mem:
            self.add_memory(mem, now=now)

    def clear(self) -> None:
        """Clear memory contents."""
        # TODO
