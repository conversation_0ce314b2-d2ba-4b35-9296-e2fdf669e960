#!/bin/bash

# Development Environment Setup Script
# This script sets up the local development environment for the Generative Agents API

set -e

echo "🚀 Setting up Generative Agents API Development Environment"
echo "=========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_status "Docker and Docker Compose are installed"

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    print_info "Creating .env file from .env.example"
    cp .env.example .env
    print_warning "Please edit .env file and add your OPENAI_API_KEY"
else
    print_status ".env file already exists"
fi

# Create necessary directories
print_info "Creating necessary directories"
mkdir -p data logs

# Set permissions
chmod 755 data logs

print_status "Directories created"

# Build the development image
print_info "Building development Docker image..."
docker-compose build api

print_status "Development environment setup complete!"

echo ""
echo "🎯 Quick Start Commands:"
echo "========================"
echo ""
echo "1. Start development server (with hot reload):"
echo "   docker-compose up api"
echo ""
echo "2. Start with Redis (for full functionality):"
echo "   docker-compose up api redis"
echo ""
echo "3. Start production-like testing:"
echo "   docker-compose --profile production up api-prod redis"
echo ""
echo "4. Start with Redis GUI tools:"
echo "   docker-compose --profile tools up api redis redis-commander"
echo ""
echo "5. Run tests:"
echo "   docker-compose run --rm api pytest"
echo ""
echo "6. Access logs:"
echo "   docker-compose logs -f api"
echo ""
echo "📍 Endpoints:"
echo "============="
echo "• Development API: http://localhost:5000"
echo "• Production-like API: http://localhost:5001"
echo "• API Documentation: http://localhost:5000/docs"
echo "• Health Check: http://localhost:5000/health"
echo "• Redis Commander: http://localhost:8081 (with --profile tools)"
echo ""
echo "🔧 Don't forget to:"
echo "=================="
echo "1. Edit .env file with your OPENAI_API_KEY"
echo "2. Check the API documentation at /docs"
echo "3. Monitor logs for any issues"
echo ""
print_status "Happy coding! 🎉"
