@echo off
echo ========================================
echo  Backend Redeploy Script
echo ========================================
echo.

echo [1/4] Stopping existing containers...
docker-compose down
if %ERRORLEVEL% neq 0 (
    echo WARNING: Failed to stop containers or no containers were running
    echo.
) else (
    echo SUCCESS: Containers stopped
    echo.
)

echo [2/4] Rebuilding backend container (this may take a few minutes)...
docker-compose build --no-cache
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to build container
    echo Please check the Docker logs above for errors
    pause
    exit /b 1
)
echo SUCCESS: Container rebuilt
echo.

echo [3/4] Starting updated containers...
docker-compose up -d
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to start containers
    echo Please check the Docker logs above for errors
    pause
    exit /b 1
)
echo SUCCESS: Containers started
echo.

echo [4/4] Checking container status...
docker-compose ps
echo.

echo ========================================
echo  Redeploy Complete!
echo ========================================
echo.
echo Your backend has been redeployed with the latest changes.
echo.
echo Useful commands:
echo   - View logs: docker-compose logs -f
echo   - Stop containers: docker-compose down
echo   - Restart containers: docker-compose restart
echo.
pause
