// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "TextAdventureModels.generated.h"

/**
 * Chapter configuration for text adventures
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FChapterConfig
{
    GENERATED_BODY()

    /** Unique identifier for the chapter */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString ChapterId;

    /** Chapter title */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString Title;

    /** System prompt that determines narration style */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString SystemPrompt;

    /** Initial scene description for the player */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString InitialScene;

    /** Chapter objectives */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    TArray<FString> Objectives;
};

/**
 * Request to create a narrator engine
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FNarratorCreateRequest
{
    GENERATED_BODY()

    /** Unique identifier for the narrator */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString NarratorId;

    /** Enable verbose logging */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    bool bVerbose;

    FNarratorCreateRequest()
    {
        bVerbose = false;
    }
};

/**
 * Evaluation of an action's feasibility
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FActionEvaluation
{
    GENERATED_BODY()

    /** Whether the action is possible */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    bool bIsPossible;

    /** Explanation for the evaluation */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString Reason;

    FActionEvaluation()
    {
        bIsPossible = true;
    }
};

/**
 * Agent reaction to an observed action
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FAgentReaction
{
    GENERATED_BODY()

    /** Agent identifier */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString AgentId;

    /** Agent name */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString AgentName;

    /** What the agent observed */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString Observation;

    /** The agent's reaction */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString Action;

    /** Whether the reaction is dialogue */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    bool bIsDialogue;

    /** Delay in seconds before the agent noticed */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    int32 DelaySeconds;

    FAgentReaction()
    {
        bIsDialogue = false;
        DelaySeconds = 0;
    }
};

/**
 * Information about the current chapter
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FChapterInfo
{
    GENERATED_BODY()

    /** Chapter identifier */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString ChapterId;

    /** Chapter title */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString Title;

    /** Current turn in the chapter */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    int32 Turn;

    FChapterInfo()
    {
        Turn = 0;
    }
};

/**
 * Request to process a user action in the text adventure
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FUserActionRequest
{
    GENERATED_BODY()

    /** The user's intended action */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString UserAction;

    /** User's current location */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString UserLocation;

    /** List of all agent IDs in the world */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    TArray<FString> AgentIds;
};

/**
 * Response from processing a user action
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FTextAdventureResponse
{
    GENERATED_BODY()

    /** Narrative text describing the outcome */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString Narration;

    /** Feasibility evaluation */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FActionEvaluation ActionEvaluation;

    /** Number of agents who observed the action */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    int32 ObservingAgents;

    /** Agent reactions */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    TArray<FAgentReaction> AgentReactions;

    /** Current chapter information */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FChapterInfo ChapterInfo;

    /** Current world turn count */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    int32 WorldTurn;

    /** ISO timestamp */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString Timestamp;

    FTextAdventureResponse()
    {
        ObservingAgents = 0;
        WorldTurn = 0;
    }
};

/**
 * Current state of a narrator engine
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FNarratorState
{
    GENERATED_BODY()

    /** Narrator identifier */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString NarratorId;

    /** Current active chapter (if any) */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FChapterInfo CurrentChapter;

    /** Whether a chapter is currently active */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    bool bHasActiveChapter;

    /** Length of narrative history */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    int32 NarrativeHistoryLength;

    FNarratorState()
    {
        bHasActiveChapter = false;
        NarrativeHistoryLength = 0;
    }
};

/**
 * Request to set up a complete text adventure
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FTextAdventureSetupRequest
{
    GENERATED_BODY()

    /** Narrator identifier */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString NarratorId;

    /** World identifier */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString WorldId;

    /** Initial chapter configuration */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FChapterConfig Chapter;

    /** World rules to add */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    TArray<FString> WorldRules;

    /** Agent IDs to include */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    TArray<FString> AgentIds;
};

/**
 * Response from text adventure setup
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FTextAdventureSetupResponse
{
    GENERATED_BODY()

    /** Whether setup was successful */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    bool bSuccess;

    /** Narrator identifier */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString NarratorId;

    /** World identifier */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString WorldId;

    /** Initial scene if chapter was started */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString InitialScene;

    /** Error message if failed */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString Error;

    FTextAdventureSetupResponse()
    {
        bSuccess = false;
    }
};
