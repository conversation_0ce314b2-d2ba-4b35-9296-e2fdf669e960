"""
Pydantic schemas for World Engine data validation and serialization.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


class WorldRuleCreate(BaseModel):
    """Schema for creating world rules."""
    rules: List[str] = Field(..., description="List of world rules to add")


class WorldEngineCreate(BaseModel):
    """Schema for creating a new world engine instance."""
    world_id: str = Field(..., description="Unique identifier for the world")
    rules: List[str] = Field(default_factory=list, description="Initial world rules")
    verbose: bool = Field(default=False, description="Enable verbose logging")


class AgentTurnResult(BaseModel):
    """Result of a single agent's turn."""
    observation: str = Field(..., description="What the agent observed")
    action: Optional[str] = Field(None, description="The agent's reaction/action")
    success: Optional[bool] = Field(None, description="Whether the action succeeded")
    is_possible: Optional[bool] = Field(None, description="Whether the action is possible")
    failure_reason: Optional[str] = Field(None, description="Reason for action failure")
    updated_status: Optional[str] = Field(None, description="Agent's updated status")
    updated_location: Optional[str] = Field(None, description="Agent's updated location")
    error: Optional[str] = Field(None, description="Error message if turn processing failed")


class TurnExecutionRequest(BaseModel):
    """Request to execute a turn in the world simulation."""
    agent_ids: List[str] = Field(..., description="List of agent IDs to include in the turn")


class TurnExecutionResponse(BaseModel):
    """Response from turn execution."""
    turn: int = Field(..., description="Turn number")
    world_id: str = Field(..., description="World identifier")
    timestamp: str = Field(..., description="ISO timestamp of turn execution")
    agent_results: Dict[str, AgentTurnResult] = Field(..., description="Results for each agent")


class WorldEngineState(BaseModel):
    """Current state of a world engine."""
    world_id: str = Field(..., description="World identifier")
    turn_count: int = Field(..., description="Current turn number")
    rule_count: int = Field(..., description="Number of world rules")
    world_state: Dict[str, Any] = Field(default_factory=dict, description="Additional world state data")


class ActionEvaluationRequest(BaseModel):
    """Request to evaluate an action's feasibility."""
    agent_id: str = Field(..., description="Agent ID")
    agent_name: str = Field(..., description="Agent name")
    action: str = Field(..., description="Intended action")
    agent_status: str = Field(..., description="Current agent status")
    observation: str = Field(..., description="Current observation")
    relevant_memories: List[str] = Field(default_factory=list, description="Recent relevant memories")


class ActionEvaluationResult(BaseModel):
    """Result of action feasibility evaluation."""
    feasibility_score: float = Field(..., description="Feasibility score (0-10)")
    relevant_rules: List[str] = Field(default_factory=list, description="World rules considered")
