"""
Entry point for the application.

When run directly, this will start a uvicorn server for the FastAPI app.
When imported by gunicorn, it provides the FastAPI app.
"""
import os
import uvicorn
from src.app import app

# Direct execution with uvicorn (for local development)
if __name__ == "__main__":
    port = int(os.environ.get("PORT", 5000))
    uvicorn.run("src.app:app", host="0.0.0.0", port=port)
