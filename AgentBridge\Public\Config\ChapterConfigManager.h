// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Models/TextAdventureModels.h"
#include "Models/ConfigurationModels.h"
#include "ChapterConfigManager.generated.h"

// Forward declarations
class UConfigurationManager;

// Legacy delegates for backward compatibility
DECLARE_DYNAMIC_DELEGATE_FourParams(FChapterFileListResponseDelegate, bool, bSuccess, const FString&, Path, const TArray<FString>&, FileNames, const TArray<FString>&, FolderNames);
DECLARE_DYNAMIC_DELEGATE_ThreeParams(FChapterFileContentResponseDelegate, bool, bSuccess, const FString&, FilePath, const FString&, JsonContent);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAllChapterConfigsRequestCompleted, bool, bOverallSuccess);

/**
 * Chapter Configuration Manager - wrapper around the unified Configuration Manager
 * Provides chapter-specific configuration loading and management functionality.
 */
UCLASS()
class AGENTBRIDGE_API UChapterConfigManager : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    //~ Begin USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    //~ End USubsystem interface

    /**
     * Loads all chapter configurations starting from the default or specified path.
     * @param OptionalPathInRepo Override the default root path from settings.
     * @param MaxRecursionDepth How many levels of subfolders to explore. 0 means only the given path.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|ChapterConfiguration")
    void LoadAllChapterConfigurationsRecursive(const FString& OptionalPathInRepo = TEXT(""), int32 MaxRecursionDepth = 1);

    /**
     * Gets a list of files and folders at a specific path within the GitHub repository.
     * @param PathInRepo The path within the repository to list.
     * @param CompletionDelegate Delegate to call when the list operation completes.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|ChapterConfiguration")
    void GetFileList(const FString& PathInRepo, FChapterFileListResponseDelegate CompletionDelegate);

    /**
     * Fetches and caches a single chapter configuration file.
     * @param FilePathInRepo The full path to the configuration file within the repository.
     * @param CompletionDelegate Delegate to call when the fetch operation completes.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|ChapterConfiguration")
    void FetchAndCacheChapterConfig(const FString& FilePathInRepo, FChapterFileContentResponseDelegate CompletionDelegate);

    /**
     * Gets a parsed chapter configuration by file path.
     * @param ConfigFilePathInRepo The path to the chapter configuration file.
     * @param OutStruct The structure to populate with configuration data.
     * @return True if the configuration was found and successfully parsed.
     */
    template<typename TStructType>
    bool GetConfiguration(const FString& ConfigFilePathInRepo, TStructType& OutStruct) const;

    /**
     * Gets a specific chapter configuration by file path.
     * @param ConfigFilePathInRepo The path to the chapter configuration file within the repository.
     * @param OutConfig The FChapterConfig structure to populate.
     * @return True if the configuration was found and successfully parsed.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|ChapterConfiguration", meta=(DisplayName="Get Chapter Config By Path"))
    bool GetChapterConfiguration(const FString& ConfigFilePathInRepo, FChapterConfig& OutConfig) const;

    /**
     * Gets a chapter configuration by Chapter ID.
     * @param ChapterId The unique identifier of the chapter to find.
     * @param OutConfig The FChapterConfig structure to populate.
     * @return True if a chapter with the specified ID was found and successfully parsed.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|ChapterConfiguration", meta=(DisplayName="Get Chapter Config By ID"))
    bool GetChapterConfigurationById(const FString& ChapterId, FChapterConfig& OutConfig) const;

    /**
     * Gets the raw chapter configuration data cache map.
     * @return A const reference to the internal map of chapter configuration data.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|ChapterConfiguration", meta=(DisplayName="Get Raw Chapter Config Cache"))
    const TMap<FString, FString>& GetRawChapterConfigCache() const;

    /**
     * Gets a list of all available chapter IDs from cached configurations.
     * @return Array of chapter IDs that have been successfully loaded and parsed.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|ChapterConfiguration", meta=(DisplayName="Get Available Chapter IDs"))
    TArray<FString> GetAvailableChapterIds() const;

    /** Delegate broadcast when LoadAllChapterConfigurationsRecursive completes. */
    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|ChapterConfiguration")
    FOnAllChapterConfigsRequestCompleted OnAllChapterConfigsLoaded;

private:
    // Delegate handlers for unified system callbacks
    UFUNCTION()
    void OnUnifiedConfigsLoaded(EConfigurationType ConfigType, bool bSuccess);

    // Get reference to the unified configuration manager
    UConfigurationManager* GetConfigurationManager() const;
};

// Template implementation for GetConfiguration
template<typename TStructType>
bool UChapterConfigManager::GetConfiguration(const FString& ConfigFilePathInRepo, TStructType& OutStruct) const
{
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (ConfigManager)
    {
        return ConfigManager->GetConfiguration(EConfigurationType::Chapter, ConfigFilePathInRepo, OutStruct);
    }
    return false;
}
