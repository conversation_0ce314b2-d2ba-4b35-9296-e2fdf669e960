@echo off
REM Quick Status Check for Development Environment
REM This script shows the status of containers and tests connectivity

echo.
echo ========================================
echo  Development Environment Status
echo ========================================
echo.

REM Check if Docker is running
docker ps >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running.
    echo.
    pause
    exit /b 1
)

echo ✅ Docker is running
echo.

REM Show container status
echo 📋 Container Status:
docker-compose ps

echo.

REM Check if API is responding
echo 🔍 Testing API connectivity...
curl -s http://localhost:5000/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Development API is responding at http://localhost:5000
    echo.
    echo 📊 Health Check Response:
    curl -s http://localhost:5000/health | jq . 2>nul || curl -s http://localhost:5000/health
) else (
    echo ❌ Development API is not responding at http://localhost:5000
)

echo.

REM Check production API if running
curl -s http://localhost:5001/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Production API is responding at http://localhost:5001
) else (
    echo ℹ️  Production API is not running (use start-prod.bat to start)
)

echo.

REM Show useful URLs
echo 🔗 Useful URLs:
echo    Development API: http://localhost:5000
echo    API Documentation: http://localhost:5000/docs
echo    Health Check: http://localhost:5000/health
echo    Production API: http://localhost:5001 (if running)

echo.
pause
