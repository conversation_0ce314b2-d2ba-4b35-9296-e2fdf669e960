"""
API routes for World Engine operations.

This module provides REST endpoints for managing world engines,
executing turns, and handling world rules.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Header, Body

from services import world_engine_service
from schemas.world_schemas import (
    WorldEngineCreate,
    WorldRuleCreate,
    TurnExecutionRequest,
    TurnExecutionResponse,
    WorldEngineState,
    AgentTurnResult
)
from config import DEFAULT_USER_ID

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()


@router.post("/create", response_model=dict, status_code=201)
async def create_world_engine(
    world_create: WorldEngineCreate,
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
):
    """
    Create a new world engine instance.
    
    Args:
        world_create: World engine creation parameters
        x_user_id: User ID from header
        
    Returns:
        World engine details
    """
    try:
        user_id = x_user_id or DEFAULT_USER_ID
        
        # Create world engine
        world_data = world_engine_service.create_world_engine(
            world_id=world_create.world_id,
            rules=world_create.rules,
            verbose=world_create.verbose,
            user_id=user_id
        )
        
        return world_data
        
    except ValueError as e:
        logger.error(f"Validation error creating world engine: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating world engine: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{world_id}", response_model=WorldEngineState)
async def get_world_engine_state(
    world_id: str,
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
):
    """
    Get the current state of a world engine.
    
    Args:
        world_id: World identifier
        x_user_id: User ID from header
        
    Returns:
        World engine state
    """
    try:
        user_id = x_user_id or DEFAULT_USER_ID
        
        world_info = world_engine_service.get_world_engine_info(world_id, user_id)
        if not world_info:
            raise HTTPException(status_code=404, detail=f"World engine '{world_id}' not found")
        
        # Convert to response schema
        state = WorldEngineState(
            world_id=world_info["world_id"],
            turn_count=world_info["turn_count"],
            rule_count=world_info["rule_count"],
            world_state=world_info.get("world_state", {})
        )
        
        return state
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting world engine state: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/{world_id}/execute-turn", response_model=TurnExecutionResponse)
async def execute_turn(
    world_id: str,
    turn_request: TurnExecutionRequest,
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
):
    """
    Execute a turn in the world simulation.
    
    Args:
        world_id: World identifier
        turn_request: Turn execution parameters
        x_user_id: User ID from header
        
    Returns:
        Turn execution results
    """
    try:
        if not turn_request.agent_ids:
            raise HTTPException(status_code=400, detail="At least one agent_id is required")
        
        user_id = x_user_id or DEFAULT_USER_ID
        
        # Execute turn
        turn_results = world_engine_service.execute_world_turn(
            world_id=world_id,
            agent_ids=turn_request.agent_ids,
            user_id=user_id
        )
        
        if not turn_results:
            raise HTTPException(status_code=500, detail="Failed to execute turn")
        
        # Convert agent results to proper schema
        agent_results_formatted = {}
        for agent_id, result in turn_results["agent_results"].items():
            agent_results_formatted[agent_id] = AgentTurnResult(**result)
        
        # Build response
        response = TurnExecutionResponse(
            turn=turn_results["turn"],
            world_id=turn_results["world_id"],
            timestamp=turn_results["timestamp"],
            agent_results=agent_results_formatted
        )
        
        return response
        
    except HTTPException:
        raise
    except ValueError as e:
        logger.error(f"Validation error executing turn: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error executing turn: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/{world_id}/rules")
async def update_world_rules(
    world_id: str,
    rule_update: WorldRuleCreate,
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
):
    """
    Add new rules to a world engine.
    
    Args:
        world_id: World identifier
        rule_update: New rules to add
        x_user_id: User ID from header
        
    Returns:
        Success message
    """
    try:
        if not rule_update.rules:
            raise HTTPException(status_code=400, detail="At least one rule is required")
        
        user_id = x_user_id or DEFAULT_USER_ID
        
        # Update rules
        success = world_engine_service.update_world_rules(
            world_id=world_id,
            new_rules=rule_update.rules,
            user_id=user_id
        )
        
        if not success:
            raise HTTPException(status_code=404, detail=f"World engine '{world_id}' not found")
        
        return {"message": f"Added {len(rule_update.rules)} rules to world '{world_id}'"}
        
    except HTTPException:
        raise
    except ValueError as e:
        logger.error(f"Validation error updating rules: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating world rules: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/", response_model=List[WorldEngineState])
async def list_world_engines(
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
):
    """
    List all world engines for a user.
    
    Args:
        x_user_id: User ID from header
        
    Returns:
        List of world engine states
    """
    try:
        user_id = x_user_id or DEFAULT_USER_ID
        
        world_engines = world_engine_service.list_world_engines(user_id)
        
        # Convert to response schema
        response = []
        for world_info in world_engines:
            state = WorldEngineState(
                world_id=world_info["world_id"],
                turn_count=world_info["turn_count"],
                rule_count=world_info["rule_count"],
                world_state=world_info.get("world_state", {})
            )
            response.append(state)
        
        return response
        
    except Exception as e:
        logger.error(f"Error listing world engines: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{world_id}")
async def delete_world_engine(
    world_id: str,
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
):
    """
    Delete a world engine.
    
    Args:
        world_id: World identifier
        x_user_id: User ID from header
        
    Returns:
        Success message
    """
    try:
        user_id = x_user_id or DEFAULT_USER_ID
        
        success = world_engine_service.delete_world_engine(world_id, user_id)
        
        if not success:
            raise HTTPException(status_code=404, detail=f"World engine '{world_id}' not found")
        
        return {"message": f"World engine '{world_id}' deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting world engine: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")
