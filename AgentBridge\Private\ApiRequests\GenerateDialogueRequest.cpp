// Copyright Epic Games, Inc. All Rights Reserved.

#include "ApiRequests/GenerateDialogueRequest.h"
#include "Json.h"
#include "JsonUtilities.h"

UGenerateDialogueRequest::UGenerateDialogueRequest()
{
}

void UGenerateDialogueRequest::SetRequestData(const FString& InAgentId, const FAgentReactionRequest& InDialogueRequestData)
{
    AgentId = InAgentId;
    ReactionRequest = InDialogueRequestData; // FAgentReactionRequest is used for the body
}

// Removed SetAgentId, SetObservation, SetCurrentTime

FString UGenerateDialogueRequest::GetVerb() const
{
    return TEXT("POST");
}

FString UGenerateDialogueRequest::GetEndpoint() const
{
    return FString::Printf(TEXT("/api/agents/%s/dialogue"), *AgentId);
}

FString UGenerateDialogueRequest::GetRequestBody() const
{
    // Create the request body
    TSharedPtr<FJsonObject> RequestObj = MakeShareable(new FJsonObject);
    RequestObj->SetStringField("observation", ReactionRequest.Observation);
    
    if (!ReactionRequest.CurrentTime.IsEmpty())
    {
        RequestObj->SetStringField("current_time", ReactionRequest.CurrentTime);
    }
    
    FString RequestBody;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestBody);
    FJsonSerializer::Serialize(RequestObj.ToSharedRef(), Writer);
    
    return RequestBody;
}

void UGenerateDialogueRequest::ProcessResponse(const FString& Response, bool bWasSuccessful, int32 StatusCode)
{
    if (bWasSuccessful && StatusCode == 200)
    {
        // Parse the response to get the dialogue data
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);
        
        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            // Extract dialogue data from response
            DialogueResponse.bIsDialogueContinuing = JsonObject->GetBoolField(TEXT("is_dialogue_continuing"));
            DialogueResponse.Response = JsonObject->GetStringField(TEXT("response"));
        }
    }
}

FAgentDialogueResponse UGenerateDialogueRequest::GetDialogueResponse() const
{
    return DialogueResponse;
}
