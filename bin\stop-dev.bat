@echo off
REM Quick Stop Script for Generative Agents API Development Environment
REM This script stops and cleans up the development environment

echo.
echo ========================================
echo  Stopping Generative Agents API (R2)
echo ========================================
echo.

REM Check if Docker is running
docker ps >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Nothing to stop.
    echo.
    pause
    exit /b 0
)

echo ✅ Docker is running
echo.

REM Stop the development environment
echo 🛑 Stopping development environment...
docker-compose down

if %errorlevel% equ 0 (
    echo ✅ Development environment stopped successfully
) else (
    echo ⚠️  Some containers may still be running
)

echo.

REM Optional: Clean up unused containers and images
set /p cleanup="🧹 Clean up unused Docker resources? (y/N): "
if /i "%cleanup%"=="y" (
    echo.
    echo 🧹 Cleaning up unused Docker resources...
    docker system prune -f
    echo ✅ Cleanup complete
)

echo.
echo 🎉 All done!
echo.
pause
