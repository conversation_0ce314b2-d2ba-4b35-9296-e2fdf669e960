// Copyright Epic Games, Inc. All Rights Reserved.

#include "AgentBridgeSettings.h"

UAgentBridgeSettings::UAgentBridgeSettings()
{
    // Default service URL
    ServiceUrl = TEXT("http://localhost:5000");

    // Default to disabled verbose logging
    bEnableVerboseLogging = false;
}

FName UAgentBridgeSettings::GetCategoryName() const
{
    return FName("Plugins");
}

FText UAgentBridgeSettings::GetSectionText() const
{
    return FText::FromString("Agent Bridge");
}

#if WITH_EDITOR
FText UAgentBridgeSettings::GetSectionDescription() const
{
    return FText::FromString("Configure settings for the Agent Bridge plugin");
}
#endif
