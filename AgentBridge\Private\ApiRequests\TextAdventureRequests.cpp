// Copyright Epic Games, Inc. All Rights Reserved.

#include "ApiRequests/TextAdventureRequests.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

// USetupTextAdventureRequest Implementation

USetupTextAdventureRequest::USetupTextAdventureRequest()
{
    SetupData = FTextAdventureSetupRequest();
}

FString USetupTextAdventureRequest::GetEndpoint() const
{
    return TEXT("/api/narrator/setup-text-adventure");
}

FString USetupTextAdventureRequest::GetVerb() const
{
    return TEXT("POST");
}

FString USetupTextAdventureRequest::GetRequestBody() const
{
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("narrator_id"), SetupData.NarratorId);
    JsonObject->SetStringField(TEXT("world_id"), SetupData.WorldId);

    // Add chapter data
    TSharedPtr<FJsonObject> ChapterObject = MakeShareable(new FJsonObject);
    ChapterObject->SetStringField(TEXT("chapter_id"), SetupData.Chapter.ChapterId);
    ChapterObject->SetStringField(TEXT("title"), SetupData.Chapter.Title);
    ChapterObject->SetStringField(TEXT("system_prompt"), SetupData.Chapter.SystemPrompt);
    ChapterObject->SetStringField(TEXT("initial_scene"), SetupData.Chapter.InitialScene);

    // Add objectives array
    TArray<TSharedPtr<FJsonValue>> ObjectivesArray;
    for (const FString& Objective : SetupData.Chapter.Objectives)
    {
        ObjectivesArray.Add(MakeShareable(new FJsonValueString(Objective)));
    }
    ChapterObject->SetArrayField(TEXT("objectives"), ObjectivesArray);

    JsonObject->SetObjectField(TEXT("chapter"), ChapterObject);

    // Add world rules array
    TArray<TSharedPtr<FJsonValue>> RulesArray;
    for (const FString& Rule : SetupData.WorldRules)
    {
        RulesArray.Add(MakeShareable(new FJsonValueString(Rule)));
    }
    JsonObject->SetArrayField(TEXT("world_rules"), RulesArray);

    // Add agent IDs array
    TArray<TSharedPtr<FJsonValue>> AgentIdsArray;
    for (const FString& AgentId : SetupData.AgentIds)
    {
        AgentIdsArray.Add(MakeShareable(new FJsonValueString(AgentId)));
    }
    JsonObject->SetArrayField(TEXT("agent_ids"), AgentIdsArray);

    // Convert to JSON string
    FString JsonString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    return JsonString;
}

void USetupTextAdventureRequest::SetSetupData(const FTextAdventureSetupRequest& InSetupData)
{
    SetupData = InSetupData;
}

// UCreateNarratorRequest Implementation

UCreateNarratorRequest::UCreateNarratorRequest()
{
    NarratorData = FNarratorCreateRequest();
}

FString UCreateNarratorRequest::GetEndpoint() const
{
    return TEXT("/api/narrator");
}

FString UCreateNarratorRequest::GetVerb() const
{
    return TEXT("POST");
}

FString UCreateNarratorRequest::GetRequestBody() const
{
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("narrator_id"), NarratorData.NarratorId);
    JsonObject->SetBoolField(TEXT("verbose"), NarratorData.bVerbose);

    FString JsonString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    return JsonString;
}

void UCreateNarratorRequest::SetNarratorData(const FNarratorCreateRequest& InNarratorData)
{
    NarratorData = InNarratorData;
}

// UAddChapterRequest Implementation

UAddChapterRequest::UAddChapterRequest()
{
    NarratorId = TEXT("");
    ChapterData = FChapterConfig();
}

FString UAddChapterRequest::GetEndpoint() const
{
    return FString::Printf(TEXT("/api/narrator/%s/chapters"), *NarratorId);
}

FString UAddChapterRequest::GetVerb() const
{
    return TEXT("POST");
}

FString UAddChapterRequest::GetRequestBody() const
{
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("chapter_id"), ChapterData.ChapterId);
    JsonObject->SetStringField(TEXT("title"), ChapterData.Title);
    JsonObject->SetStringField(TEXT("system_prompt"), ChapterData.SystemPrompt);
    JsonObject->SetStringField(TEXT("initial_scene"), ChapterData.InitialScene);

    // Add objectives array
    TArray<TSharedPtr<FJsonValue>> ObjectivesArray;
    for (const FString& Objective : ChapterData.Objectives)
    {
        ObjectivesArray.Add(MakeShareable(new FJsonValueString(Objective)));
    }
    JsonObject->SetArrayField(TEXT("objectives"), ObjectivesArray);

    FString JsonString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    return JsonString;
}

void UAddChapterRequest::SetChapterData(const FString& InNarratorId, const FChapterConfig& InChapterData)
{
    NarratorId = InNarratorId;
    ChapterData = InChapterData;
}

// UStartChapterRequest Implementation

UStartChapterRequest::UStartChapterRequest()
{
    NarratorId = TEXT("");
    ChapterId = TEXT("");
}

FString UStartChapterRequest::GetEndpoint() const
{
    return FString::Printf(TEXT("/api/narrator/%s/start-chapter"), *NarratorId);
}

FString UStartChapterRequest::GetVerb() const
{
    return TEXT("POST");
}

FString UStartChapterRequest::GetRequestBody() const
{
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("chapter_id"), ChapterId);

    FString JsonString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    return JsonString;
}

void UStartChapterRequest::SetChapterInfo(const FString& InNarratorId, const FString& InChapterId)
{
    NarratorId = InNarratorId;
    ChapterId = InChapterId;
}

// UProcessUserActionRequest Implementation

UProcessUserActionRequest::UProcessUserActionRequest()
{
    NarratorId = TEXT("");
    WorldId = TEXT("");
    ActionData = FUserActionRequest();
}

FString UProcessUserActionRequest::GetEndpoint() const
{
    return FString::Printf(TEXT("/api/narrator/%s/process-action?world_id=%s"), *NarratorId, *WorldId);
}

FString UProcessUserActionRequest::GetVerb() const
{
    return TEXT("POST");
}

FString UProcessUserActionRequest::GetRequestBody() const
{
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("user_action"), ActionData.UserAction);
    JsonObject->SetStringField(TEXT("user_location"), ActionData.UserLocation);

    // Add agent IDs array
    TArray<TSharedPtr<FJsonValue>> AgentIdsArray;
    for (const FString& AgentId : ActionData.AgentIds)
    {
        AgentIdsArray.Add(MakeShareable(new FJsonValueString(AgentId)));
    }
    JsonObject->SetArrayField(TEXT("agent_ids"), AgentIdsArray);

    FString JsonString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    return JsonString;
}

void UProcessUserActionRequest::SetActionData(const FString& InNarratorId, const FString& InWorldId, const FUserActionRequest& InActionData)
{
    NarratorId = InNarratorId;
    WorldId = InWorldId;
    ActionData = InActionData;
}
