// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "ApiRequests/BaseApiRequest.h"
#include "Models/AgentModels.h"
#include "GenerateDialogueRequest.generated.h"

/**
 * API request to generate a dialogue response from an agent
 */
UCLASS(BlueprintType)
class AGENTBRIDGE_API UGenerateDialogueRequest : public UBaseApiRequest
{
    GENERATED_BODY()

public:
    UGenerateDialogueRequest();

    /**
     * Set the data for this request
     * @param InAgentId - The agent ID to use
     * @param InDialogueRequestData - The dialogue request data (observation, current time), uses FAgentReactionRequest struct
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|API")
    void SetRequestData(const FString& InAgentId, const FAgentReactionRequest& InDialogueRequestData);

    // Override BaseApiRequest methods
    virtual FString GetVerb() const override;
    virtual FString GetEndpoint() const override;
    virtual FString GetRequestBody() const override;
    virtual void ProcessResponse(const FString& Response, bool bWasSuccessful, int32 StatusCode) override;

    /**
     * Get the dialogue response
     * @return The dialogue response
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|API")
    FAgentDialogueResponse GetDialogueResponse() const;

private:
    // The agent ID
    UPROPERTY()
    FString AgentId;

    // The reaction request data
    UPROPERTY()
    FAgentReactionRequest ReactionRequest;

    // The dialogue response
    UPROPERTY()
    FAgentDialogueResponse DialogueResponse;
};
