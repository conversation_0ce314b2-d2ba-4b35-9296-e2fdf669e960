// Copyright Epic Games, Inc. All Rights Reserved.

#include "AgentBridge.h"
#include "WebServiceSubsystem.h"
#include "AgentCommunicationComponent.h"
#include "HttpModule.h"
#include "AgentBridgeSettings.h"
#include "Logging/LogMacros.h"

// Define log category
DEFINE_LOG_CATEGORY(LogAgentBridge);

#define LOCTEXT_NAMESPACE "FAgentBridgeModule"

void FAgentBridgeModule::StartupModule()
{
	// This code will execute after your module is loaded into memory; the exact timing is specified in the .uplugin file per-module

	// Load required modules
	FModuleManager::LoadModuleChecked<FHttpModule>("HTTP");

	// Register settings
	if (ISettingsModule* SettingsModule = FModuleManager::GetModulePtr<ISettingsModule>("Settings"))
	{
		// Register the settings
		SettingsModule->RegisterSettings("Project", "Plugins", "AgentBridge",
			LOCTEXT("AgentBridgeSettingsName", "Agent Bridge"),
			LOCTEXT("AgentBridgeSettingsDescription", "Configure the Agent Bridge plugin"),
			GetMutableDefault<UAgentBridgeSettings>()
		);
	}

	// Log that the module has started
	UE_LOG(LogAgentBridge, Log, TEXT("AgentBridge module has started"));
}

void FAgentBridgeModule::ShutdownModule()
{
	// This function may be called during shutdown to clean up your module.  For modules that support dynamic reloading,
	// we call this function before unloading the module.

	// Unregister settings
	if (ISettingsModule* SettingsModule = FModuleManager::GetModulePtr<ISettingsModule>("Settings"))
	{
		SettingsModule->UnregisterSettings("Project", "Plugins", "AgentBridge");
	}
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FAgentBridgeModule, AgentBridge)