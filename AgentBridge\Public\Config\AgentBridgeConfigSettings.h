// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DeveloperSettings.h"
#include "AgentBridgeConfigSettings.generated.h"

/**
 * Settings for the AgentBridge plugin, configurable in Project Settings.
 */
UCLASS(Config = Engine, DefaultConfig, meta = (DisplayName = "AgentBridge Configuration"))
class AGENTBRIDGE_API UAgentBridgeConfigSettings : public UDeveloperSettings
{
    GENERATED_BODY()

public:
    UAgentBridgeConfigSettings();

    /** The base URL for the backend configuration service. E.g., https://your-backend.com/api/v1/configs */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Configuration Service")
    FString BackendConfigServiceURL;

    /** Optional API Key if the backend configuration service requires authentication. */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Configuration Service")
    FString BackendAPIKey;

    /** Default root path in the GitHub repository from which to start discovering configurations. E.g., AllConfigs/ or leave empty for root. */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Configuration Service", AdvancedDisplay)
    FString DefaultRootConfigPathInRepo;

    /** If true, prefer loading configurations from a local directory (specified below) instead of the backend service. Useful for development or offline mode. */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Local Fallback")
    bool bPreferLocalConfigs;

    /** Path to the local directory containing configuration files, used if bPreferLocalConfigs is true or backend is unavailable. */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Local Fallback", meta = (EditCondition = "bPreferLocalConfigs"))
    FString LocalConfigDirectoryPath;

#if WITH_EDITOR
    virtual FText GetSectionText() const override;
    virtual FText GetSectionDescription() const override;
#endif
};
