"""
API routes for Narrator Engine and Text Adventure operations.

This module provides REST endpoints for managing narrator engines,
chapters, and text adventure game loops.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Header, Body

from services import narrator_service
from schemas.text_adventure_schemas import (
    NarratorEngineCreate,
    ChapterCreate,
    UserActionRequest,
    TextAdventureResponse,
    NarratorState,
    NarrativeHistoryEntry,
    StartChapterRequest,
    StartChapterResponse,
    TextAdventureSetupRequest,
    TextAdventureSetupResponse,
    AgentReaction,
    ActionEvaluation,
    ChapterInfo
)
from config import DEFAULT_USER_ID

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()


@router.post("/create", response_model=dict, status_code=201)
async def create_narrator_engine(
    narrator_create: NarratorEngineCreate,
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
):
    """
    Create a new narrator engine instance.
    
    Args:
        narrator_create: Narrator engine creation parameters
        x_user_id: User ID from header
        
    Returns:
        Narrator engine details
    """
    try:
        user_id = x_user_id or DEFAULT_USER_ID
        
        # Create narrator engine
        narrator_data = narrator_service.create_narrator_engine(
            narrator_id=narrator_create.narrator_id,
            verbose=narrator_create.verbose,
            user_id=user_id
        )
        
        return narrator_data
        
    except ValueError as e:
        logger.error(f"Validation error creating narrator engine: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating narrator engine: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{narrator_id}", response_model=NarratorState)
async def get_narrator_state(
    narrator_id: str,
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
):
    """
    Get the current state of a narrator engine.
    
    Args:
        narrator_id: Narrator identifier
        x_user_id: User ID from header
        
    Returns:
        Narrator engine state
    """
    try:
        user_id = x_user_id or DEFAULT_USER_ID
        
        narrator_info = narrator_service.get_narrator_engine_info(narrator_id, user_id)
        if not narrator_info:
            raise HTTPException(status_code=404, detail=f"Narrator engine '{narrator_id}' not found")
        
        # Convert to response schema
        state = NarratorState(
            narrator_id=narrator_info["narrator_id"],
            chapters=narrator_info["chapters"],
            current_chapter=narrator_info["current_chapter"],
            narrative_history_length=narrator_info["narrative_history_length"]
        )
        
        return state
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting narrator state: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/{narrator_id}/chapters", status_code=201)
async def add_chapter(
    narrator_id: str,
    chapter: ChapterCreate,
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
):
    """
    Add a chapter to a narrator engine.
    
    Args:
        narrator_id: Narrator identifier
        chapter: Chapter creation parameters
        x_user_id: User ID from header
        
    Returns:
        Success message
    """
    try:
        user_id = x_user_id or DEFAULT_USER_ID
        
        # Create and add chapter
        success = narrator_service.create_and_add_chapter(
            narrator_id=narrator_id,
            chapter_data=chapter.dict(),
            user_id=user_id
        )
        
        if not success:
            raise HTTPException(status_code=404, detail=f"Narrator engine '{narrator_id}' not found")
        
        return {"message": f"Chapter '{chapter.title}' added successfully"}
        
    except HTTPException:
        raise
    except ValueError as e:
        logger.error(f"Validation error adding chapter: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error adding chapter: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/{narrator_id}/chapters/start", response_model=StartChapterResponse)
async def start_chapter(
    narrator_id: str,
    request: StartChapterRequest,
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
):
    """
    Start a chapter in the narrator engine.
    
    Args:
        narrator_id: Narrator identifier
        request: Chapter start request
        x_user_id: User ID from header
        
    Returns:
        Initial scene and success status
    """
    try:
        user_id = x_user_id or DEFAULT_USER_ID
        
        initial_scene = narrator_service.start_chapter(
            narrator_id=narrator_id,
            chapter_id=request.chapter_id,
            user_id=user_id
        )
        
        if initial_scene is None:
            return StartChapterResponse(
                success=False,
                initial_scene=None,
                error="Failed to start chapter"
            )
        
        return StartChapterResponse(
            success=True,
            initial_scene=initial_scene,
            error=None
        )
        
    except Exception as e:
        logger.error(f"Error starting chapter: {e}", exc_info=True)
        return StartChapterResponse(
            success=False,
            initial_scene=None,
            error=str(e)
        )


@router.post("/{narrator_id}/process-action", response_model=TextAdventureResponse)
async def process_user_action(
    narrator_id: str,
    world_id: str,
    action_request: UserActionRequest,
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
):
    """
    Process a user action in the text adventure.
    
    This is the main game loop endpoint that:
    1. Evaluates action feasibility
    2. Finds observing agents
    3. Triggers agent reactions
    4. Generates narrative response
    
    Args:
        narrator_id: Narrator identifier
        world_id: World identifier
        action_request: User action parameters
        x_user_id: User ID from header
        
    Returns:
        Narrative response and game state updates
    """
    try:
        user_id = x_user_id or DEFAULT_USER_ID
        
        # Process the user action
        result = narrator_service.process_user_action(
            narrator_id=narrator_id,
            world_id=world_id,
            user_action=action_request.user_action,
            user_location=action_request.user_location,
            agent_ids=action_request.agent_ids,
            user_id=user_id
        )
        
        if not result:
            raise HTTPException(status_code=500, detail="Failed to process user action")
        
        # Convert to response schema
        response = TextAdventureResponse(
            narration=result["narration"],
            action_evaluation=ActionEvaluation(**result["action_evaluation"]),
            observing_agents=result["observing_agents"],
            agent_reactions=[AgentReaction(**r) for r in result["agent_reactions"]],
            chapter_info=ChapterInfo(**result["chapter_info"]),
            world_turn=result["world_turn"],
            timestamp=result["timestamp"]
        )
        
        return response
        
    except HTTPException:
        raise
    except ValueError as e:
        logger.error(f"Validation error processing action: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error processing user action: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{narrator_id}/history", response_model=List[NarrativeHistoryEntry])
async def get_narrative_history(
    narrator_id: str,
    limit: Optional[int] = None,
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
):
    """
    Get the narrative history for a narrator.
    
    Args:
        narrator_id: Narrator identifier
        limit: Maximum number of entries to return
        x_user_id: User ID from header
        
    Returns:
        List of narrative history entries
    """
    try:
        user_id = x_user_id or DEFAULT_USER_ID
        
        history = narrator_service.get_narrative_history(
            narrator_id=narrator_id,
            user_id=user_id,
            limit=limit
        )
        
        if history is None:
            raise HTTPException(status_code=404, detail=f"Narrator engine '{narrator_id}' not found")
        
        # Convert to response schema
        return [NarrativeHistoryEntry(**entry) for entry in history]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting narrative history: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/{narrator_id}/chapters/complete")
async def complete_chapter(
    narrator_id: str,
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
):
    """
    Complete the current chapter.
    
    Args:
        narrator_id: Narrator identifier
        x_user_id: User ID from header
        
    Returns:
        Success message
    """
    try:
        user_id = x_user_id or DEFAULT_USER_ID
        
        success = narrator_service.complete_current_chapter(
            narrator_id=narrator_id,
            user_id=user_id
        )
        
        if not success:
            raise HTTPException(status_code=404, detail=f"Narrator engine '{narrator_id}' not found")
        
        return {"message": "Chapter completed successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing chapter: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/", response_model=List[NarratorState])
async def list_narrators(
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
):
    """
    List all narrator engines for a user.
    
    Args:
        x_user_id: User ID from header
        
    Returns:
        List of narrator states
    """
    try:
        user_id = x_user_id or DEFAULT_USER_ID
        
        narrators = narrator_service.list_narrator_engines(user_id)
        
        # Convert to response schema
        return [
            NarratorState(
                narrator_id=n["narrator_id"],
                chapters=n["chapters"],
                current_chapter=n["current_chapter"],
                narrative_history_length=n["narrative_history_length"]
            )
            for n in narrators
        ]
        
    except Exception as e:
        logger.error(f"Error listing narrators: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{narrator_id}")
async def delete_narrator(
    narrator_id: str,
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
):
    """
    Delete a narrator engine.
    
    Args:
        narrator_id: Narrator identifier
        x_user_id: User ID from header
        
    Returns:
        Success message
    """
    try:
        user_id = x_user_id or DEFAULT_USER_ID
        
        success = narrator_service.delete_narrator_engine(
            narrator_id=narrator_id,
            user_id=user_id
        )
        
        if not success:
            raise HTTPException(status_code=404, detail=f"Narrator engine '{narrator_id}' not found")
        
        return {"message": f"Narrator engine '{narrator_id}' deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting narrator: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/setup", response_model=TextAdventureSetupResponse)
async def setup_text_adventure(
    setup_request: TextAdventureSetupRequest,
    x_user_id: Optional[str] = Header(None, alias="X-User-Id")
):
    """
    Set up a complete text adventure with narrator, world, and initial chapter.
    
    This is a convenience endpoint that creates all necessary components
    for a text adventure in one request.
    
    Args:
        setup_request: Complete setup parameters
        x_user_id: User ID from header
        
    Returns:
        Setup result with initial scene
    """
    try:
        user_id = x_user_id or DEFAULT_USER_ID
        
        # Import required services
        from services import world_engine_service
        
        # Create narrator engine
        narrator_data = narrator_service.create_narrator_engine(
            narrator_id=setup_request.narrator_id,
            verbose=False,
            user_id=user_id
        )
        
        # Create world engine
        world_data = world_engine_service.create_world_engine(
            world_id=setup_request.world_id,
            rules=setup_request.world_rules,
            verbose=False,
            user_id=user_id
        )
        
        # Add chapter to narrator
        chapter_success = narrator_service.create_and_add_chapter(
            narrator_id=setup_request.narrator_id,
            chapter_data=setup_request.chapter.dict(),
            user_id=user_id
        )
        
        if not chapter_success:
            return TextAdventureSetupResponse(
                success=False,
                narrator_id=setup_request.narrator_id,
                world_id=setup_request.world_id,
                initial_scene=None,
                error="Failed to add chapter"
            )
        
        # Start the chapter
        initial_scene = narrator_service.start_chapter(
            narrator_id=setup_request.narrator_id,
            chapter_id=setup_request.chapter.chapter_id,
            user_id=user_id
        )
        
        return TextAdventureSetupResponse(
            success=True,
            narrator_id=setup_request.narrator_id,
            world_id=setup_request.world_id,
            initial_scene=initial_scene,
            error=None
        )
        
    except Exception as e:
        logger.error(f"Error setting up text adventure: {e}", exc_info=True)
        return TextAdventureSetupResponse(
            success=False,
            narrator_id=setup_request.narrator_id,
            world_id=setup_request.world_id,
            initial_scene=None,
            error=str(e)
        )
