// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DeveloperSettings.h"
#include "AgentBridgeSettings.generated.h"

/**
 * Configuration settings for the AgentBridge plugin.
 * These settings can be edited in Project Settings > Plugins > Agent Bridge
 */
UCLASS(Config=Game, DefaultConfig, meta = (DisplayName = "Agent Bridge"))
class AGENTBRIDGE_API UAgentBridgeSettings : public UDeveloperSettings
{
    GENERATED_BODY()

public:
    UAgentBridgeSettings();

    /** The base URL for the backend service API */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "API", meta = (DisplayName = "Service URL"))
    FString ServiceUrl;

    /** Enable verbose logging for requests and responses */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Debugging", meta = (DisplayName = "Enable Verbose Logging"))
    bool bEnableVerboseLogging;

    //~ Begin UDeveloperSettings Interface
    virtual FName GetCategoryName() const override;
    virtual FText GetSectionText() const override;
#if WITH_EDITOR
    virtual FText GetSectionDescription() const override;
#endif
    //~ End UDeveloperSettings Interface
};
