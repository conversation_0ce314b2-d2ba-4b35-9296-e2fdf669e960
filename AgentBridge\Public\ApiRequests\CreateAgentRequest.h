// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "ApiRequests/BaseApiRequest.h"
#include "Models/AgentModels.h"
#include "CreateAgentRequest.generated.h"

/**
 * API request to create an agent
 */
UCLASS(BlueprintType)
class AGENTBRIDGE_API UCreateAgentRequest : public UBaseApiRequest
{
    GENERATED_BODY()

public:
    UCreateAgentRequest();

    /**
     * Set the agent data for this request
     * @param InAgentData - The agent data to use
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|API")
    void SetAgentData(const FAgentCreateRequest& InAgentData);

    // Override BaseApiRequest methods
    virtual FString GetVerb() const override;
    virtual FString GetEndpoint() const override;
    virtual FString GetRequestBody() const override;
    virtual void ProcessResponse(const FString& Response, bool bWasSuccessful, int32 StatusCode) override;

    /**
     * Get the request data as a JSON object
     * @return The request data as a JSON object
     */
    TSharedPtr<FJsonObject> GetRequestDataAsJson() const;

    /**
     * Get the request data as a JSON string
     * @return The request data as a JSON string
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|API")
    FString GetRequestDataAsString() const;

    /**
     * Get the created agent data
     * @return The created agent data
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|API")
    FAgentData GetCreatedAgentData() const;

private:
    // The agent data to create
    UPROPERTY()
    FAgentCreateRequest AgentData;

    // The created agent data
    UPROPERTY()
    FAgentData CreatedAgentData;
};
