"""
Chapter class for managing chapter-specific narration settings.

This module provides the Chapter class that encapsulates chapter-specific
narration settings including system prompts, initial scenes, and objectives.
"""

import logging
from typing import Optional, Dict, Any, List
from datetime import datetime

logger = logging.getLogger(__name__)


class Chapter:
    """
    Represents a chapter in the text adventure.
    
    Each chapter has its own narration style, objectives, and initial scene.
    """
    
    def __init__(
        self,
        chapter_id: str,
        title: str,
        system_prompt: str,
        initial_scene: str,
        objectives: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize a chapter.
        
        Args:
            chapter_id: Unique identifier for the chapter
            title: Chapter title
            system_prompt: System prompt that determines narration style
            initial_scene: Initial scene description for the player
            objectives: Optional list of chapter objectives
            metadata: Optional metadata for the chapter
        """
        self.chapter_id = chapter_id
        self.title = title
        self.system_prompt = system_prompt
        self.initial_scene = initial_scene
        self.objectives = objectives or []
        self.metadata = metadata or {}
        self.started_at = None
        self.completed_at = None
        self.turn_count = 0
        
        logger.info(f"Chapter '{title}' (ID: {chapter_id}) initialized")
    
    def start(self) -> str:
        """
        Start the chapter and return the initial scene.
        
        Returns:
            The initial scene description
        """
        self.started_at = datetime.now()
        logger.info(f"Chapter '{self.title}' started at {self.started_at}")
        return self.initial_scene
    
    def complete(self) -> None:
        """Mark the chapter as completed."""
        self.completed_at = datetime.now()
        logger.info(f"Chapter '{self.title}' completed at {self.completed_at}")
    
    def increment_turn(self) -> None:
        """Increment the turn counter for this chapter."""
        self.turn_count += 1
    
    def is_completed(self) -> bool:
        """Check if the chapter is completed."""
        return self.completed_at is not None
    
    def get_narration_context(self) -> Dict[str, Any]:
        """
        Get context for narration generation.
        
        Returns:
            Dictionary containing chapter context
        """
        return {
            "chapter_id": self.chapter_id,
            "title": self.title,
            "turn_count": self.turn_count,
            "objectives": self.objectives,
            "metadata": self.metadata
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the chapter to a dictionary.
        
        Returns:
            Dictionary representation of the chapter
        """
        return {
            "chapter_id": self.chapter_id,
            "title": self.title,
            "system_prompt": self.system_prompt,
            "initial_scene": self.initial_scene,
            "objectives": self.objectives,
            "metadata": self.metadata,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "turn_count": self.turn_count
        }
