@echo off
REM Fast Backend Redeploy Script for Generative Agents API
REM This script rebuilds and redeploys the backend with Docker cache for faster builds
REM Use this when you haven't added new dependencies

echo.
echo ========================================
echo  Fast Backend Redeploy Script
echo ========================================
echo.
echo 🚀 This script uses Docker cache for faster rebuilds
echo 💡 Use redeploy-backend.bat for full rebuild when adding dependencies
echo.

REM Check if Docker is running
docker ps >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop first.
    echo.
    pause
    exit /b 1
)

echo ✅ Docker is running
echo.

REM Change to backend root directory (parent of bin)
cd /d "%~dp0\.."
echo 📁 Working directory: %CD%
echo.

echo [1/4] Stopping existing containers...
docker-compose down
if %ERRORLEVEL% neq 0 (
    echo ⚠️  Warning: Failed to stop containers or no containers were running
    echo.
) else (
    echo ✅ Containers stopped successfully
    echo.
)

echo [2/4] Rebuilding backend container with cache (faster build)...
docker-compose build
if %ERRORLEVEL% neq 0 (
    echo ❌ ERROR: Failed to build container
    echo Please check the Docker logs above for errors
    echo.
    echo 💡 If you added new dependencies, try running redeploy-backend.bat instead
    echo.
    pause
    exit /b 1
)
echo ✅ Container rebuilt successfully
echo.

echo [3/4] Starting updated containers...
docker-compose up -d
if %ERRORLEVEL% neq 0 (
    echo ❌ ERROR: Failed to start containers
    echo Please check the Docker logs above for errors
    echo.
    pause
    exit /b 1
)
echo ✅ Containers started successfully
echo.

echo [4/4] Checking container status...
docker-compose ps
echo.

echo ========================================
echo  🎉 Fast Redeploy Complete!
echo ========================================
echo.
echo Your backend has been redeployed with the latest changes using Docker cache.
echo.
echo 🔗 Useful URLs:
echo    Development API: http://localhost:5000
echo    API Documentation: http://localhost:5000/docs
echo    Health Check: http://localhost:5000/health
echo.
echo 💡 Useful commands:
echo    View logs: docker-compose logs -f
echo    Stop containers: docker-compose down
echo    Restart containers: docker-compose restart
echo.
echo 📝 Note: If you added new dependencies, use redeploy-backend.bat for a full rebuild
echo.
pause
