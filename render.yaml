services:
  - type: web
    name: generative-agents-api
    env: python
    buildCommand: pip install --upgrade pip && pip install -r requirements.txt
    startCommand: gunicorn -c gunicorn.conf.py main:app
    envVars:
      - key: OPENAI_API_KEY
        sync: false
      - key: DEFAULT_MODEL_NAME
        value: gpt-4o
      - key: DEFAULT_EMBEDDINGS_MODEL
        value: text-embedding-3-small
      - key: DEBUG
        value: false
      - key: LOG_LEVEL
        value: INFO
      - key: MEMORY_IMPORTANCE_WEIGHT
        value: 0.15
      - key: REFLECTION_THRESHOLD
        value: 0.5
      - key: MEMORY_K
        value: 10
      - key: ENABLE_PERSISTENCE
        value: true
      - key: DATA_DIR
        value: data
      - key: DEFAULT_USER_ID
        value: default
      - key: ENABLE_REFLECTION
        value: "true"
      - key: GUNICORN_TIMEOUT
        value: "300"
      - key: GUNICORN_WORKERS
        value: "2"
      - key: GUNICORN_MAX_REQUESTS
        value: "1000"
      - key: GUNICORN_LOG_LEVEL
        value: "info"
      - key: REQUEST_TIMEOUT
        value: "240"
      - key: REQUEST_WARNING_THRESHOLD
        value: "60"
    autoDeploy: true
