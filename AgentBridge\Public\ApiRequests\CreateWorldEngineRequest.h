// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "ApiRequests/BaseApiRequest.h"
#include "Models/WorldEngineModels.h"
#include "CreateWorldEngineRequest.generated.h"

/**
 * API request to create a world engine
 */
UCLASS(BlueprintType)
class AGENTBRIDGE_API UCreateWorldEngineRequest : public UBaseApiRequest
{
    GENERATED_BODY()

public:
    UCreateWorldEngineRequest();

    /**
     * Set the world engine data for this request
     * @param InWorldData - The world engine data to use
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|WorldEngine")
    void SetWorldData(const FWorldEngineCreateRequest& InWorldData);

    // Override BaseApiRequest methods
    virtual FString GetVerb() const override;
    virtual FString GetEndpoint() const override;
    virtual FString GetRequestBody() const override;
    virtual void ProcessResponse(const FString& Response, bool bWasSuccessful, int32 StatusCode) override;

    /**
     * Get the created world engine state
     * @return The created world engine state
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|WorldEngine")
    FWorldEngineState GetCreatedWorldState() const;

private:
    // The world engine data to create
    UPROPERTY()
    FWorldEngineCreateRequest WorldData;

    // The created world engine state
    UPROPERTY()
    FWorldEngineState CreatedWorldState;
};
