# Development Environment Setup Script for Windows PowerShell
# This script sets up the local development environment for the Generative Agents API

param(
    [switch]$SkipBuild = $false
)

Write-Host "🚀 Setting up Generative Agents API Development Environment" -ForegroundColor Green
Write-Host "==========================================================" -ForegroundColor Green

function Write-Success {
    param($Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param($Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

# Check if Docker is installed
try {
    $dockerVersion = docker --version
    Write-Success "Docker is installed: $dockerVersion"
} catch {
    Write-Error "Docker is not installed. Please install Docker Desktop first."
    exit 1
}

# Check if Docker Compose is available
try {
    $composeVersion = docker-compose --version
    Write-Success "Docker Compose is available: $composeVersion"
} catch {
    Write-Error "Docker Compose is not available. Please ensure Docker Desktop is running."
    exit 1
}

# Create .env file if it doesn't exist
if (-not (Test-Path ".env")) {
    Write-Info "Creating .env file from .env.example"
    Copy-Item ".env.example" ".env"
    Write-Warning "Please edit .env file and add your OPENAI_API_KEY"
} else {
    Write-Success ".env file already exists"
}

# Create necessary directories
Write-Info "Creating necessary directories"
$directories = @("data", "logs")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Success "Created directory: $dir"
    } else {
        Write-Success "Directory already exists: $dir"
    }
}

# Build the development image
if (-not $SkipBuild) {
    Write-Info "Building development Docker image..."
    try {
        docker-compose build api
        Write-Success "Development image built successfully"
    } catch {
        Write-Error "Failed to build development image"
        exit 1
    }
} else {
    Write-Info "Skipping Docker image build"
}

Write-Success "Development environment setup complete!"

Write-Host ""
Write-Host "🎯 Quick Start Commands:" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. Start development server (with hot reload):"
Write-Host "   docker-compose up api" -ForegroundColor White
Write-Host ""
Write-Host "2. Start with Redis (for full functionality):"
Write-Host "   docker-compose up api redis" -ForegroundColor White
Write-Host ""
Write-Host "3. Start production-like testing:"
Write-Host "   docker-compose --profile production up api-prod redis" -ForegroundColor White
Write-Host ""
Write-Host "4. Start with Redis GUI tools:"
Write-Host "   docker-compose --profile tools up api redis redis-commander" -ForegroundColor White
Write-Host ""
Write-Host "5. Run tests:"
Write-Host "   docker-compose run --rm api pytest" -ForegroundColor White
Write-Host ""
Write-Host "6. Access logs:"
Write-Host "   docker-compose logs -f api" -ForegroundColor White
Write-Host ""
Write-Host "📍 Endpoints:" -ForegroundColor Cyan
Write-Host "=============" -ForegroundColor Cyan
Write-Host "• Development API: http://localhost:5000" -ForegroundColor White
Write-Host "• Production-like API: http://localhost:5001" -ForegroundColor White
Write-Host "• API Documentation: http://localhost:5000/docs" -ForegroundColor White
Write-Host "• Health Check: http://localhost:5000/health" -ForegroundColor White
Write-Host "• Redis Commander: http://localhost:8081 (with --profile tools)" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Don't forget to:" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan
Write-Host "1. Edit .env file with your OPENAI_API_KEY" -ForegroundColor White
Write-Host "2. Check the API documentation at /docs" -ForegroundColor White
Write-Host "3. Monitor logs for any issues" -ForegroundColor White
Write-Host ""
Write-Success "Happy coding! 🎉"
