import logging
import time
import os
import colorlog
from functools import wraps
from fastapi import Request, HTTPException
import inspect

# Configure a basic logger if not already configured by the main app
# This is more of a fallback; ideally, the main app.py configures logging.
# logging.basicConfig(level=logging.INFO, format='%(levelname)s %(asctime)s %(name)s:%(funcName)s %(message)s') # Removed, app.py handles config

def setup_logger(module_name=None):
    """
    Sets up a logger with colored output similar to the main app configuration.
    This function can be called by any module that needs logging, including worker processes.

    Args:
        module_name (str, optional): The name of the module. If None, uses the caller's module name.

    Returns:
        logging.Logger: A configured logger instance
    """
    # If module_name is not provided, try to get it from the caller's frame
    if module_name is None:
        frame = inspect.currentframe().f_back
        module_name = inspect.getmodule(frame).__name__ if frame and inspect.getmodule(frame) else "__main__"

    # Configure logging level from environment variable
    log_level_str = os.environ.get("LOG_LEVEL", "INFO").upper()
    numeric_log_level = getattr(logging, log_level_str, logging.INFO)

    # Get or create a logger for the specified module
    logger = logging.getLogger(module_name)
    logger.setLevel(numeric_log_level)

    # Clear existing handlers to avoid duplicate logs
    if logger.hasHandlers():
        logger.handlers.clear()

    # Create a colored console handler
    handler = colorlog.StreamHandler()
    formatter = colorlog.ColoredFormatter(
        "%(log_color)s%(levelname)-8s%(reset)s %(asctime)s %(name)s:%(funcName)s %(blue)s%(message)s",
        datefmt='%Y-%m-%d %H:%M:%S',
        reset=True,
        log_colors={
            'DEBUG':    'cyan',
            'INFO':     'green',
            'WARNING':  'yellow',
            'ERROR':    'red',
            'CRITICAL': 'red,bg_white',
        },
        secondary_log_colors={},
        style='%'
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)

    return logger

def get_request_details(request: Request):
    """Extracts relevant details from the request object for logging."""
    return {
        "method": request.method,
        "url": str(request.url),
        "client_host": request.client.host if request.client else "N/A",
        "path_params": request.path_params,
        "query_params": str(request.query_params)
    }

def log_route_details(logger: logging.Logger):
    """
    A decorator to log details about API route execution, including request,
    response, and errors.
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Try to find the Request object in the arguments
            request_obj = None
            for arg in args:
                if isinstance(arg, Request):
                    request_obj = arg
                    break
            if not request_obj: # Fallback if Request is not a direct arg (e.g. if it's in kwargs or not passed)
                 # Attempt to find request in kwargs, common if using `request: Request = Depends()`
                if 'request' in kwargs and isinstance(kwargs['request'], Request):
                    request_obj = kwargs['request']
                else: # If request object is not found, log a warning and proceed without request details
                    # This might happen if the decorator is used on a non-route function
                    # or a route that doesn't have Request as a parameter.
                    # For FastAPI, `Request` object needs to be an explicit parameter of the route function.
                    # If it's not, we can't automatically get request details here.
                    # We will rely on the route function itself to pass `request: Request`
                    pass


            func_name = func.__name__
            module_name = inspect.getmodule(func).__name__ if inspect.getmodule(func) else "unknown_module"
            log_prefix = f"Endpoint ({module_name}.{func_name})"

            if request_obj:
                req_details = get_request_details(request_obj)
                logger.info(f"{log_prefix}: Received request {req_details['method']} {req_details['url']} from {req_details['client_host']}. Path: {req_details['path_params']}, Query: {req_details['query_params']}")
            else:
                logger.info(f"{log_prefix}: Execution started (Request object not available to decorator).")


            start_time = time.time()
            try:
                response = await func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"{log_prefix}: Successfully processed request in {duration:.4f}s.")
                # Consider logging response details if appropriate and not too verbose/sensitive
                return response
            except HTTPException as http_exc:
                duration = time.time() - start_time
                logger.warning(f"{log_prefix}: Failed with HTTPException ({http_exc.status_code}) in {duration:.4f}s. Detail: {http_exc.detail}")
                raise http_exc # Re-raise the exception
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"{log_prefix}: Failed with unhandled exception in {duration:.4f}s: {str(e)}", exc_info=True)
                # Raise a generic HTTPException to prevent leaking internal error details
                raise HTTPException(status_code=500, detail="Internal server error")
        return wrapper
    return decorator

# Example of how to get a logger instance for a specific module
# In your route files, you would typically do:
# logger = logging.getLogger(__name__)
# @log_route_details(logger)
# async def my_route_function(request: Request, ...):
#     ...
