// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "ApiRequests/BaseApiRequest.h"
#include "Models/AgentModels.h"
#include "GenerateReactionRequest.generated.h"

/**
 * API request to generate a reaction from an agent
 */
UCLASS(BlueprintType)
class AGENTBRIDGE_API UGenerateReactionRequest : public UBaseApiRequest
{
    GENERATED_BODY()

public:
    UGenerateReactionRequest();

    /**
     * Set the data for this request
     * @param InAgentId - The agent ID to use
     * @param InReactionRequestData - The reaction request data (observation, current time)
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|API")
    void SetRequestData(const FString& InAgentId, const FAgentReactionRequest& InReactionRequestData);

    // Override BaseApiRequest methods
    virtual FString GetVerb() const override;
    virtual FString GetEndpoint() const override;
    virtual FString GetRequestBody() const override;
    virtual void ProcessResponse(const FString& Response, bool bWasSuccessful, int32 StatusCode) override;

    /**
     * Get the reaction response
     * @return The reaction response
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|API")
    FAgentReactionResponse GetReactionResponse() const;

private:
    // The agent ID
    UPROPERTY()
    FString AgentId;

    // The reaction request data
    UPROPERTY()
    FAgentReactionRequest ReactionRequest;

    // The reaction response
    UPROPERTY()
    FAgentReactionResponse ReactionResponse;
};
