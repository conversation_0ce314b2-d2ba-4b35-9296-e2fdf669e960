// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "ApiRequests/BaseApiRequest.h"
#include "Models/TextAdventureModels.h"
#include "TextAdventureRequests.generated.h"

/**
 * Request to set up a complete text adventure
 */
UCLASS(BlueprintType)
class AGENTBRIDGE_API USetupTextAdventureRequest : public UBaseApiRequest
{
    GENERATED_BODY()

public:
    USetupTextAdventureRequest();

    // UBaseApiRequest implementation
    virtual FString GetEndpoint() const override;
    virtual FString GetVerb() const override;
    virtual FString GetRequestBody() const override;

    /** Set the setup data */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|TextAdventure")
    void SetSetupData(const FTextAdventureSetupRequest& InSetupData);

    /** Get the setup data */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|TextAdventure")
    FTextAdventureSetupRequest GetSetupData() const { return SetupData; }

protected:
    /** The setup data for the text adventure */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FTextAdventureSetupRequest SetupData;
};

/**
 * Request to create a narrator engine
 */
UCLASS(BlueprintType)
class AGENTBRIDGE_API UCreateNarratorRequest : public UBaseApiRequest
{
    GENERATED_BODY()

public:
    UCreateNarratorRequest();

    // UBaseApiRequest implementation
    virtual FString GetEndpoint() const override;
    virtual FString GetVerb() const override;
    virtual FString GetRequestBody() const override;

    /** Set the narrator data */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|TextAdventure")
    void SetNarratorData(const FNarratorCreateRequest& InNarratorData);

    /** Get the narrator data */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|TextAdventure")
    FNarratorCreateRequest GetNarratorData() const { return NarratorData; }

protected:
    /** The narrator creation data */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FNarratorCreateRequest NarratorData;
};

/**
 * Request to add a chapter to a narrator
 */
UCLASS(BlueprintType)
class AGENTBRIDGE_API UAddChapterRequest : public UBaseApiRequest
{
    GENERATED_BODY()

public:
    UAddChapterRequest();

    // UBaseApiRequest implementation
    virtual FString GetEndpoint() const override;
    virtual FString GetVerb() const override;
    virtual FString GetRequestBody() const override;

    /** Set the narrator ID and chapter data */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|TextAdventure")
    void SetChapterData(const FString& InNarratorId, const FChapterConfig& InChapterData);

    /** Get the narrator ID */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|TextAdventure")
    FString GetNarratorId() const { return NarratorId; }

    /** Get the chapter data */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|TextAdventure")
    FChapterConfig GetChapterData() const { return ChapterData; }

protected:
    /** The narrator ID */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString NarratorId;

    /** The chapter configuration data */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FChapterConfig ChapterData;
};

/**
 * Request to start a chapter
 */
UCLASS(BlueprintType)
class AGENTBRIDGE_API UStartChapterRequest : public UBaseApiRequest
{
    GENERATED_BODY()

public:
    UStartChapterRequest();

    // UBaseApiRequest implementation
    virtual FString GetEndpoint() const override;
    virtual FString GetVerb() const override;
    virtual FString GetRequestBody() const override;

    /** Set the narrator ID and chapter ID */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|TextAdventure")
    void SetChapterInfo(const FString& InNarratorId, const FString& InChapterId);

    /** Get the narrator ID */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|TextAdventure")
    FString GetNarratorId() const { return NarratorId; }

    /** Get the chapter ID */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|TextAdventure")
    FString GetChapterId() const { return ChapterId; }

protected:
    /** The narrator ID */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString NarratorId;

    /** The chapter ID to start */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString ChapterId;
};

/**
 * Request to process a user action
 */
UCLASS(BlueprintType)
class AGENTBRIDGE_API UProcessUserActionRequest : public UBaseApiRequest
{
    GENERATED_BODY()

public:
    UProcessUserActionRequest();

    // UBaseApiRequest implementation
    virtual FString GetEndpoint() const override;
    virtual FString GetVerb() const override;
    virtual FString GetRequestBody() const override;

    /** Set the action data */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|TextAdventure")
    void SetActionData(const FString& InNarratorId, const FString& InWorldId, const FUserActionRequest& InActionData);

    /** Get the narrator ID */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|TextAdventure")
    FString GetNarratorId() const { return NarratorId; }

    /** Get the world ID */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|TextAdventure")
    FString GetWorldId() const { return WorldId; }

    /** Get the action data */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|TextAdventure")
    FUserActionRequest GetActionData() const { return ActionData; }

protected:
    /** The narrator ID */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString NarratorId;

    /** The world ID */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FString WorldId;

    /** The user action data */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|TextAdventure")
    FUserActionRequest ActionData;
};
