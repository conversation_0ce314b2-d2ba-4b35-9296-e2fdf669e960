// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "ApiRequests/BaseApiRequest.h"
#include "Models/MemoryModels.h"
#include "AddMemoryRequest.generated.h"

/**
 * API request to add a memory to an agent
 */
UCLASS(BlueprintType)
class AGENTBRIDGE_API UAddMemoryRequest : public UBaseApiRequest
{
    GENERATED_BODY()

public:
    UAddMemoryRequest();

    /**
     * Set the memory data for this request
     * @param InMemoryData - The memory data to use
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|API")
    void SetMemoryData(const FMemoryCreateRequest& InMemoryData);

    // Override BaseApiRequest methods
    virtual FString GetVerb() const override;
    virtual FString GetEndpoint() const override;
    virtual FString GetRequestBody() const override;
    virtual void ProcessResponse(const FString& Response, bool bWasSuccessful, int32 StatusCode) override;

    /**
     * Get the created memory data
     * @return The created memory data
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|API")
    FMemoryData GetCreatedMemoryData() const;

private:
    // The memory data to create
    UPROPERTY()
    FMemoryCreateRequest MemoryData;

    // The created memory data
    UPROPERTY()
    FMemoryData CreatedMemoryData;
};
