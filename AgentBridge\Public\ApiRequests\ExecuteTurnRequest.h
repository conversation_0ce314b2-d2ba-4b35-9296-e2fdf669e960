// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "ApiRequests/BaseApiRequest.h"
#include "Models/WorldEngineModels.h"
#include "ExecuteTurnRequest.generated.h"

/**
 * API request to execute a turn in the world simulation
 */
UCLASS(BlueprintType)
class AGENTBRIDGE_API UExecuteTurnRequest : public UBaseApiRequest
{
    GENERATED_BODY()

public:
    UExecuteTurnRequest();

    /**
     * Set the world ID for this request
     * @param InWorldId - The world identifier
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|WorldEngine")
    void SetWorldId(const FString& InWorldId);

    /**
     * Set the turn execution data for this request
     * @param InTurnData - The turn execution data to use
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|WorldEngine")
    void SetTurnData(const FTurnExecutionRequest& InTurnData);

    // Override BaseApiRequest methods
    virtual FString GetVerb() const override;
    virtual FString GetEndpoint() const override;
    virtual FString GetRequestBody() const override;
    virtual void ProcessResponse(const FString& Response, bool bWasSuccessful, int32 StatusCode) override;

    /**
     * Get the turn execution response
     * @return The turn execution response
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|WorldEngine")
    FTurnExecutionResponse GetTurnResponse() const;

private:
    // The world ID
    UPROPERTY()
    FString WorldId;

    // The turn execution request data
    UPROPERTY()
    FTurnExecutionRequest TurnData;

    // The turn execution response
    UPROPERTY()
    FTurnExecutionResponse TurnResponse;
};
