// Copyright Epic Games, Inc. All Rights Reserved.

#include "Config/ConfigurationManager.h"
#include "Config/AgentBridgeConfigSettings.h"
#include "HttpModule.h"
#include "Interfaces/IHttpRequest.h"
#include "Interfaces/IHttpResponse.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonReader.h"
#include "JsonObjectConverter.h"
#include "HAL/FileManager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "GenericPlatform/GenericPlatformHttp.h"

DEFINE_LOG_CATEGORY_STATIC(LogConfigurationManager, Log, All);

void UConfigurationManager::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    UE_LOG(LogConfigurationManager, Log, TEXT("ConfigurationManager Initialized."));

    // Initialize configuration types
    InitializeConfigurationTypes();
}

void UConfigurationManager::Deinitialize()
{
    UE_LOG(LogConfigurationManager, Log, TEXT("ConfigurationManager Deinitialized."));
    // Cancel any pending HTTP requests if necessary
    Super::Deinitialize();
}

void UConfigurationManager::InitializeConfigurationTypes()
{
    // Initialize configuration type information
    ConfigurationTypes.Empty();

    ConfigurationTypes.Add(EConfigurationType::Agent,
        FConfigurationTypeInfo(EConfigurationType::Agent, TEXT("Agents/"), TEXT("Agent Configuration")));

    ConfigurationTypes.Add(EConfigurationType::Chapter,
        FConfigurationTypeInfo(EConfigurationType::Chapter, TEXT("Chapters/"), TEXT("Chapter Configuration")));

    ConfigurationTypes.Add(EConfigurationType::WorldInstance,
        FConfigurationTypeInfo(EConfigurationType::WorldInstance, TEXT("WorldInstances/"), TEXT("World Instance Configuration")));

    UE_LOG(LogConfigurationManager, Log, TEXT("Initialized %d configuration types"), ConfigurationTypes.Num());
}

const UAgentBridgeConfigSettings* UConfigurationManager::GetSettings() const
{
    return GetDefault<UAgentBridgeConfigSettings>();
}

FConfigurationTypeInfo UConfigurationManager::GetConfigurationTypeInfo(EConfigurationType ConfigType) const
{
    const FConfigurationTypeInfo* TypeInfo = ConfigurationTypes.Find(ConfigType);
    if (TypeInfo)
    {
        return *TypeInfo;
    }

    // Return default if not found
    return FConfigurationTypeInfo();
}

FString UConfigurationManager::GetCacheKey(EConfigurationType ConfigType, const FString& FilePath) const
{
    return FString::Printf(TEXT("%d_%s"), (int32)ConfigType, *FilePath);
}

void UConfigurationManager::LoadAllConfigurationsRecursive(EConfigurationType ConfigType, const FString& OptionalPathInRepo, int32 MaxRecursionDepth)
{
    // Clear existing cache for this configuration type
    ConfigurationCache.FindOrAdd(ConfigType).Empty();

    const UAgentBridgeConfigSettings* Settings = GetSettings();
    if (!Settings)
    {
        UE_LOG(LogConfigurationManager, Error, TEXT("Failed to get AgentBridgeConfigSettings."));
        OnAllConfigsLoaded.Broadcast(ConfigType, false);
        return;
    }

    FString RootPathToLoad = OptionalPathInRepo;
    if (RootPathToLoad.IsEmpty())
    {
        // Use the default path for this configuration type
        const FConfigurationTypeInfo TypeInfo = GetConfigurationTypeInfo(ConfigType);
        RootPathToLoad = TypeInfo.DefaultPath;

        // Fall back to general config path if type-specific path is empty
        if (RootPathToLoad.IsEmpty())
        {
            RootPathToLoad = Settings->DefaultRootConfigPathInRepo;
        }
    }

    // Ensure path ends with a slash if not empty, for consistency
    if (!RootPathToLoad.IsEmpty() && !RootPathToLoad.EndsWith(TEXT("/")))
    {
        RootPathToLoad += TEXT("/");
    }

    if (Settings->bPreferLocalConfigs || Settings->BackendConfigServiceURL.IsEmpty())
    {
        UE_LOG(LogConfigurationManager, Log, TEXT("Loading %s configurations from local directory, starting at relative path: '%s' in '%s'"),
            *GetConfigurationTypeInfo(ConfigType).DisplayName, *RootPathToLoad, *Settings->LocalConfigDirectoryPath);

        LoadConfigurationsFromLocalRecursive(ConfigType, Settings->LocalConfigDirectoryPath, RootPathToLoad, 0, MaxRecursionDepth);

        // Local loading is synchronous
        const TMap<FString, FString>* TypeCache = ConfigurationCache.Find(ConfigType);
        OnAllConfigsLoaded.Broadcast(ConfigType, TypeCache && TypeCache->Num() > 0);
    }
    else
    {
        UE_LOG(LogConfigurationManager, Log, TEXT("Loading %s configurations from backend service, starting at path: '%s'"),
            *GetConfigurationTypeInfo(ConfigType).DisplayName, *RootPathToLoad);

        // Start recursive loading from backend
        FString EncodedPath = FGenericPlatformHttp::UrlEncode(RootPathToLoad);
        FString URL = FString::Printf(TEXT("%s/files?path=%s"), *Settings->BackendConfigServiceURL, *EncodedPath);

        TSharedRef<IHttpRequest, ESPMode::ThreadSafe> Request = FHttpModule::Get().CreateRequest();
        Request->SetURL(URL);
        Request->SetVerb(TEXT("GET"));

        if (!Settings->BackendAPIKey.IsEmpty())
        {
            Request->SetHeader(TEXT("Authorization"), FString::Printf(TEXT("Bearer %s"), *Settings->BackendAPIKey));
        }

        Request->OnProcessRequestComplete().BindUObject(this, &UConfigurationManager::OnRecursiveFileListRequestComplete, ConfigType, RootPathToLoad, 0, MaxRecursionDepth);
        Request->ProcessRequest();
    }
}

const TMap<FString, FString>& UConfigurationManager::GetRawConfigCache(EConfigurationType ConfigType) const
{
    const TMap<FString, FString>* TypeCache = ConfigurationCache.Find(ConfigType);
    if (TypeCache)
    {
        return *TypeCache;
    }

    // Return empty map if not found
    static TMap<FString, FString> EmptyMap;
    return EmptyMap;
}

void UConfigurationManager::LoadConfigurationsFromLocalRecursive(EConfigurationType ConfigType, const FString& RootDirectory, const FString& CurrentRelativePath, int32 CurrentDepth, int32 MaxDepth)
{
    if (CurrentDepth > MaxDepth) return;

    IFileManager& FileManager = IFileManager::Get();
    FString CurrentFullDirectory = FPaths::Combine(RootDirectory, CurrentRelativePath);

    UE_LOG(LogConfigurationManager, Verbose, TEXT("Scanning directory: %s (depth %d/%d)"), *CurrentFullDirectory, CurrentDepth, MaxDepth);

    // Get all files and directories in the current directory
    TArray<FString> FoundFiles;
    TArray<FString> FoundDirectories;

    FileManager.FindFiles(FoundFiles, *FPaths::Combine(CurrentFullDirectory, TEXT("*.json")), true, false);
    FileManager.FindFiles(FoundDirectories, *FPaths::Combine(CurrentFullDirectory, TEXT("*")), false, true);

    // Process JSON files in current directory
    for (const FString& FileName : FoundFiles)
    {
        FString FullFilePath = FPaths::Combine(CurrentFullDirectory, FileName);
        FString RelativeFilePath = FPaths::Combine(CurrentRelativePath, FileName);

        // Normalize path separators for consistency
        RelativeFilePath = RelativeFilePath.Replace(TEXT("\\"), TEXT("/"));

        if (ReadAndCacheLocalFile(ConfigType, FullFilePath, RelativeFilePath))
        {
            UE_LOG(LogConfigurationManager, Verbose, TEXT("Successfully loaded %s config: %s"),
                *GetConfigurationTypeInfo(ConfigType).DisplayName, *RelativeFilePath);
        }
    }

    // Recursively process subdirectories if we haven't reached max depth
    if (CurrentDepth < MaxDepth)
    {
        for (const FString& DirName : FoundDirectories)
        {
            // Skip hidden directories and common non-config directories
            if (!DirName.StartsWith(TEXT(".")) && DirName != TEXT(".."))
            {
                FString NewRelativePath = FPaths::Combine(CurrentRelativePath, DirName);
                LoadConfigurationsFromLocalRecursive(ConfigType, RootDirectory, NewRelativePath, CurrentDepth + 1, MaxDepth);
            }
        }
    }
}

bool UConfigurationManager::ReadAndCacheLocalFile(EConfigurationType ConfigType, const FString& FullFilePath, const FString& PathInRepo)
{
    FString JsonContent;
    if (!FFileHelper::LoadFileToString(JsonContent, *FullFilePath))
    {
        UE_LOG(LogConfigurationManager, Warning, TEXT("Failed to read file: %s"), *FullFilePath);
        return false;
    }

    if (JsonContent.IsEmpty())
    {
        UE_LOG(LogConfigurationManager, Warning, TEXT("File is empty: %s"), *FullFilePath);
        return false;
    }

    // Store in the appropriate cache
    TMap<FString, FString>& TypeCache = ConfigurationCache.FindOrAdd(ConfigType);
    TypeCache.Add(PathInRepo, JsonContent);

    UE_LOG(LogConfigurationManager, Verbose, TEXT("Cached %s config from local file: %s"),
        *GetConfigurationTypeInfo(ConfigType).DisplayName, *PathInRepo);

    return true;
}

void UConfigurationManager::GetFileList(EConfigurationType ConfigType, const FString& PathInRepo, FConfigFileListResponseDelegate CompletionDelegate)
{
    const UAgentBridgeConfigSettings* Settings = GetSettings();
    if (!Settings || Settings->BackendConfigServiceURL.IsEmpty())
    {
        UE_LOG(LogConfigurationManager, Error, TEXT("BackendConfigServiceURL is not set for GetFileList."));
        CompletionDelegate.ExecuteIfBound(false, PathInRepo, {}, {});
        return;
    }

    FString EncodedPath = FGenericPlatformHttp::UrlEncode(PathInRepo);
    FString URL = FString::Printf(TEXT("%s/files?path=%s"), *Settings->BackendConfigServiceURL, *EncodedPath);

    TSharedRef<IHttpRequest, ESPMode::ThreadSafe> Request = FHttpModule::Get().CreateRequest();
    Request->SetURL(URL);
    Request->SetVerb(TEXT("GET"));

    if (!Settings->BackendAPIKey.IsEmpty())
    {
        Request->SetHeader(TEXT("Authorization"), FString::Printf(TEXT("Bearer %s"), *Settings->BackendAPIKey));
    }

    Request->OnProcessRequestComplete().BindUObject(this, &UConfigurationManager::OnPublicFileListRequestComplete, CompletionDelegate, PathInRepo);
    Request->ProcessRequest();

    UE_LOG(LogConfigurationManager, Verbose, TEXT("Requesting file list for %s configs at path: %s"),
        *GetConfigurationTypeInfo(ConfigType).DisplayName, *PathInRepo);
}

void UConfigurationManager::FetchAndCacheConfig(EConfigurationType ConfigType, const FString& FilePathInRepo, FConfigFileContentResponseDelegate CompletionDelegate)
{
    const UAgentBridgeConfigSettings* Settings = GetSettings();
    if (!Settings || Settings->BackendConfigServiceURL.IsEmpty())
    {
        UE_LOG(LogConfigurationManager, Error, TEXT("BackendConfigServiceURL is not set for FetchAndCacheConfig."));
        CompletionDelegate.ExecuteIfBound(false, FilePathInRepo, TEXT(""));
        return;
    }

    FString EncodedPath = FGenericPlatformHttp::UrlEncode(FilePathInRepo);
    FString URL = FString::Printf(TEXT("%s/file?path=%s"), *Settings->BackendConfigServiceURL, *EncodedPath);

    TSharedRef<IHttpRequest, ESPMode::ThreadSafe> Request = FHttpModule::Get().CreateRequest();
    Request->SetURL(URL);
    Request->SetVerb(TEXT("GET"));

    if (!Settings->BackendAPIKey.IsEmpty())
    {
        Request->SetHeader(TEXT("Authorization"), FString::Printf(TEXT("Bearer %s"), *Settings->BackendAPIKey));
    }

    Request->OnProcessRequestComplete().BindUObject(this, &UConfigurationManager::OnPublicFileContentRequestComplete, CompletionDelegate, FilePathInRepo);
    Request->ProcessRequest();

    UE_LOG(LogConfigurationManager, Verbose, TEXT("Fetching %s config file: %s"),
        *GetConfigurationTypeInfo(ConfigType).DisplayName, *FilePathInRepo);
}

void UConfigurationManager::OnRecursiveFileListRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, EConfigurationType ConfigType, FString OriginalPath, int32 CurrentDepth, int32 MaxDepth)
{
    if (!bWasSuccessful || !Response.IsValid() || Response->GetResponseCode() != 200)
    {
        UE_LOG(LogConfigurationManager, Error, TEXT("Failed to fetch file list for %s configs at path '%s'. Code: %d"),
            *GetConfigurationTypeInfo(ConfigType).DisplayName, *OriginalPath, Response.IsValid() ? Response->GetResponseCode() : -1);
        OnAllConfigsLoaded.Broadcast(ConfigType, false);
        return;
    }

    FString JsonString = Response->GetContentAsString();
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
    {
        // Parse file and folder lists
        TArray<FString> FileNames;
        TArray<FString> FolderNames;

        const TArray<TSharedPtr<FJsonValue>>* FilesArray;
        if (JsonObject->TryGetArrayField(TEXT("files"), FilesArray))
        {
            for (const TSharedPtr<FJsonValue>& FileValue : *FilesArray)
            {
                FString FileName = FileValue->AsString();
                if (FileName.EndsWith(TEXT(".json")))
                {
                    FileNames.Add(FileName);
                }
            }
        }

        const TArray<TSharedPtr<FJsonValue>>* FoldersArray;
        if (JsonObject->TryGetArrayField(TEXT("folders"), FoldersArray))
        {
            for (const TSharedPtr<FJsonValue>& FolderValue : *FoldersArray)
            {
                FolderNames.Add(FolderValue->AsString());
            }
        }

        // Fetch all JSON files in current directory
        for (const FString& FileName : FileNames)
        {
            FString FullFilePath = OriginalPath + FileName;
            FetchAndCacheConfig(ConfigType, FullFilePath, FConfigFileContentResponseDelegate());
        }

        // Recursively process subdirectories if we haven't reached max depth
        if (CurrentDepth < MaxDepth)
        {
            for (const FString& FolderName : FolderNames)
            {
                FString SubPath = OriginalPath + FolderName + TEXT("/");

                const UAgentBridgeConfigSettings* Settings = GetSettings();
                if (Settings && !Settings->BackendConfigServiceURL.IsEmpty())
                {
                    FString EncodedPath = FGenericPlatformHttp::UrlEncode(SubPath);
                    FString URL = FString::Printf(TEXT("%s/files?path=%s"), *Settings->BackendConfigServiceURL, *EncodedPath);

                    TSharedRef<IHttpRequest, ESPMode::ThreadSafe> SubRequest = FHttpModule::Get().CreateRequest();
                    SubRequest->SetURL(URL);
                    SubRequest->SetVerb(TEXT("GET"));

                    if (!Settings->BackendAPIKey.IsEmpty())
                    {
                        SubRequest->SetHeader(TEXT("Authorization"), FString::Printf(TEXT("Bearer %s"), *Settings->BackendAPIKey));
                    }

                    SubRequest->OnProcessRequestComplete().BindUObject(this, &UConfigurationManager::OnRecursiveFileListRequestComplete, ConfigType, SubPath, CurrentDepth + 1, MaxDepth);
                    SubRequest->ProcessRequest();
                }
            }
        }

        // If this was the root request and we have no more pending requests, broadcast completion
        if (CurrentDepth == 0)
        {
            // Note: In a real implementation, you'd want to track pending requests
            // For now, we'll broadcast success after a short delay to allow file fetches to complete
            const TMap<FString, FString>* TypeCache = ConfigurationCache.Find(ConfigType);
            OnAllConfigsLoaded.Broadcast(ConfigType, TypeCache && TypeCache->Num() > 0);
        }
    }
    else
    {
        UE_LOG(LogConfigurationManager, Error, TEXT("Failed to parse file list JSON for %s configs at path '%s': %s"),
            *GetConfigurationTypeInfo(ConfigType).DisplayName, *OriginalPath, *JsonString.Left(200));
        OnAllConfigsLoaded.Broadcast(ConfigType, false);
    }
}

void UConfigurationManager::OnPublicFileListRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FConfigFileListResponseDelegate UserDelegate, FString OriginalPath)
{
    if (!bWasSuccessful || !Response.IsValid() || Response->GetResponseCode() != 200)
    {
        UE_LOG(LogConfigurationManager, Error, TEXT("Public API: Failed to fetch file list at path '%s'. Code: %d"),
            *OriginalPath, Response.IsValid() ? Response->GetResponseCode() : -1);
        UserDelegate.ExecuteIfBound(false, OriginalPath, {}, {});
        return;
    }

    FString JsonString = Response->GetContentAsString();
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
    {
        TArray<FString> FileNames;
        TArray<FString> FolderNames;

        const TArray<TSharedPtr<FJsonValue>>* FilesArray;
        if (JsonObject->TryGetArrayField(TEXT("files"), FilesArray))
        {
            for (const TSharedPtr<FJsonValue>& FileValue : *FilesArray)
            {
                FileNames.Add(FileValue->AsString());
            }
        }

        const TArray<TSharedPtr<FJsonValue>>* FoldersArray;
        if (JsonObject->TryGetArrayField(TEXT("folders"), FoldersArray))
        {
            for (const TSharedPtr<FJsonValue>& FolderValue : *FoldersArray)
            {
                FolderNames.Add(FolderValue->AsString());
            }
        }

        UE_LOG(LogConfigurationManager, Verbose, TEXT("Public API: Successfully fetched file list for path '%s'. Files: %d, Folders: %d"),
            *OriginalPath, FileNames.Num(), FolderNames.Num());
        UserDelegate.ExecuteIfBound(true, OriginalPath, FileNames, FolderNames);
    }
    else
    {
        UE_LOG(LogConfigurationManager, Error, TEXT("Public API: Failed to parse file list JSON for path '%s': %s"),
            *OriginalPath, *JsonString.Left(200));
        UserDelegate.ExecuteIfBound(false, OriginalPath, {}, {});
    }
}

void UConfigurationManager::OnPublicFileContentRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FConfigFileContentResponseDelegate UserDelegate, FString OriginalFilePath)
{
    if (!bWasSuccessful || !Response.IsValid() || Response->GetResponseCode() != 200)
    {
        UE_LOG(LogConfigurationManager, Error, TEXT("Public API: Failed to fetch config content for '%s'. Code: %d"),
            *OriginalFilePath, Response.IsValid() ? Response->GetResponseCode() : -1);
        UserDelegate.ExecuteIfBound(false, OriginalFilePath, TEXT(""));
        return;
    }

    FString JsonContent = Response->GetContentAsString();
    if (!JsonContent.IsEmpty())
    {
        UE_LOG(LogConfigurationManager, Verbose, TEXT("Public API: Successfully fetched config content for '%s'. Size: %d bytes"),
            *OriginalFilePath, JsonContent.Len());
        UserDelegate.ExecuteIfBound(true, OriginalFilePath, JsonContent);
    }
    else
    {
        UE_LOG(LogConfigurationManager, Warning, TEXT("Public API: Received empty content for config '%s'"), *OriginalFilePath);
        UserDelegate.ExecuteIfBound(false, OriginalFilePath, TEXT(""));
    }
}
