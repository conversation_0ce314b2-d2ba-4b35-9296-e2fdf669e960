// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Models/ConfigurationModels.h"
#include "WorldInstanceConfigManager.generated.h"

// Forward declarations
class UConfigurationManager;

// Delegates for asynchronous operations
DECLARE_DYNAMIC_DELEGATE_FourParams(FWorldInstanceFileListResponseDelegate, bool, bSuccess, const FString&, Path, const TArray<FString>&, FileNames, const TArray<FString>&, FolderNames);
DECLARE_DYNAMIC_DELEGATE_ThreeParams(FWorldInstanceFileContentResponseDelegate, bool, bSuccess, const FString&, FilePath, const FString&, JsonContent);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAllWorldInstanceConfigsRequestCompleted, bool, bOverallSuccess);

/**
 * World Instance Configuration Manager - wrapper around the unified Configuration Manager
 * Provides world instance-specific configuration loading and management functionality.
 */
UCLASS()
class AGENTBRIDGE_API UWorldInstanceConfigManager : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    //~ Begin USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    //~ End USubsystem interface

    /**
     * Loads all world instance configurations starting from the default or specified path.
     * @param OptionalPathInRepo Override the default root path from settings.
     * @param MaxRecursionDepth How many levels of subfolders to explore. 0 means only the given path.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|WorldInstanceConfiguration")
    void LoadAllWorldInstanceConfigurationsRecursive(const FString& OptionalPathInRepo = TEXT(""), int32 MaxRecursionDepth = 1);

    /**
     * Gets a list of files and folders at a specific path within the GitHub repository.
     * @param PathInRepo The path within the repository to list.
     * @param CompletionDelegate Delegate to call when the list operation completes.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|WorldInstanceConfiguration")
    void GetFileList(const FString& PathInRepo, FWorldInstanceFileListResponseDelegate CompletionDelegate);

    /**
     * Fetches and caches a single world instance configuration file.
     * @param FilePathInRepo The full path to the configuration file within the repository.
     * @param CompletionDelegate Delegate to call when the fetch operation completes.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|WorldInstanceConfiguration")
    void FetchAndCacheWorldInstanceConfig(const FString& FilePathInRepo, FWorldInstanceFileContentResponseDelegate CompletionDelegate);

    /**
     * Gets a parsed world instance configuration by file path.
     * @param ConfigFilePathInRepo The path to the world instance configuration file.
     * @param OutStruct The structure to populate with configuration data.
     * @return True if the configuration was found and successfully parsed.
     */
    template<typename TStructType>
    bool GetConfiguration(const FString& ConfigFilePathInRepo, TStructType& OutStruct) const;

    /**
     * Gets a specific world instance configuration by file path.
     * @param ConfigFilePathInRepo The path to the world instance configuration file within the repository.
     * @param OutConfig The FWorldInstanceConfig structure to populate.
     * @return True if the configuration was found and successfully parsed.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|WorldInstanceConfiguration", meta=(DisplayName="Get World Instance Config By Path"))
    bool GetWorldInstanceConfiguration(const FString& ConfigFilePathInRepo, FWorldInstanceConfig& OutConfig) const;

    /**
     * Gets a world instance configuration by World ID.
     * @param WorldId The unique identifier of the world instance to find.
     * @param OutConfig The FWorldInstanceConfig structure to populate.
     * @return True if a world instance with the specified ID was found and successfully parsed.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|WorldInstanceConfiguration", meta=(DisplayName="Get World Instance Config By ID"))
    bool GetWorldInstanceConfigurationById(const FString& WorldId, FWorldInstanceConfig& OutConfig) const;

    /**
     * Gets the raw world instance configuration data cache map.
     * @return A const reference to the internal map of world instance configuration data.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|WorldInstanceConfiguration", meta=(DisplayName="Get Raw World Instance Config Cache"))
    const TMap<FString, FString>& GetRawWorldInstanceConfigCache() const;

    /**
     * Gets a list of all available world instance IDs from cached configurations.
     * @return Array of world instance IDs that have been successfully loaded and parsed.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|WorldInstanceConfiguration", meta=(DisplayName="Get Available World Instance IDs"))
    TArray<FString> GetAvailableWorldInstanceIds() const;

    /** Delegate broadcast when LoadAllWorldInstanceConfigurationsRecursive completes. */
    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|WorldInstanceConfiguration")
    FOnAllWorldInstanceConfigsRequestCompleted OnAllWorldInstanceConfigsLoaded;

private:
    // Delegate handlers for unified system callbacks
    UFUNCTION()
    void OnUnifiedConfigsLoaded(EConfigurationType ConfigType, bool bSuccess);

    // Get reference to the unified configuration manager
    UConfigurationManager* GetConfigurationManager() const;
};

// Template implementation for GetConfiguration
template<typename TStructType>
bool UWorldInstanceConfigManager::GetConfiguration(const FString& ConfigFilePathInRepo, TStructType& OutStruct) const
{
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (ConfigManager)
    {
        return ConfigManager->GetConfiguration(EConfigurationType::WorldInstance, ConfigFilePathInRepo, OutStruct);
    }
    return false;
}
