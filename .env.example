# API Configuration
DEBUG=False
LOG_LEVEL=INFO

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
DEFAULT_MODEL_NAME=gpt-4o
DEFAULT_EMBEDDINGS_MODEL=text-embedding-3-small

# Memory Configuration
MEMORY_IMPORTANCE_WEIGHT=0.15
REFLECTION_THRESHOLD=0.5
MEMORY_K=10

# File Persistence Configuration
ENABLE_PERSISTENCE=True
DATA_DIR=data
DEFAULT_USER_ID=default

# Timeout Configuration (for worker timeout fixes)
GUNICORN_TIMEOUT=300
GUNICORN_WORKERS=2
GUNICORN_MAX_REQUESTS=1000
GUNICORN_LOG_LEVEL=info
REQUEST_TIMEOUT=240
REQUEST_WARNING_THRESHOLD=60

# Reflection Configuration
ENABLE_REFLECTION=true
