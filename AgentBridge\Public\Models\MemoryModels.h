// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "MemoryModels.generated.h"

/**
 * Data structure for memory information
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FMemoryData
{
    GENERATED_BODY()

    /** The unique identifier for the memory */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    FString Id;

    /** The agent ID this memory belongs to */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    FString AgentId;

    /** The memory content */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    FString Content;

    /** The memory importance (0.0 to 1.0) */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    float Importance;

    /** When the memory was created */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    FString CreatedAt;

    /** The memory description (optional) */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    FString Description;
};

/**
 * Data structure for memory creation request
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FMemoryCreateRequest
{
    GENERATED_BODY()

    /** The agent ID this memory belongs to */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    FString AgentId;

    /** The memory content */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    FString MemoryContent;

    /** The memory importance (0.0 to 1.0) */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    float Importance;

    /** The memory description (optional) */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    FString Description;

    /** The current time (ISO format, optional) */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    FString CurrentTime;
};

/**
 * Data structure for memory query request
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FMemoryQueryRequest
{
    GENERATED_BODY()

    /** The agent ID to query memories from */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    FString AgentId;

    /** The query text */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    FString Query;

    /** The maximum number of memories to return */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    int32 MaxCount;

    /** Whether to include importance scores */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    bool bIncludeScores;
};

/**
 * Data structure for memory query result
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FMemoryQueryResult
{
    GENERATED_BODY()

    /** The memory content */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    FString Content;

    /** The memory importance score */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    float Score;

    /** When the memory was created */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Memory")
    FString CreatedAt;
};
