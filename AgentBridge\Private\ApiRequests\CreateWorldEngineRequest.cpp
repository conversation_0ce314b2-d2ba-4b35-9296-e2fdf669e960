// Copyright Epic Games, Inc. All Rights Reserved.

#include "ApiRequests/CreateWorldEngineRequest.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "AgentBridge.h"

UCreateWorldEngineRequest::UCreateWorldEngineRequest()
{
    // Constructor
}

void UCreateWorldEngineRequest::SetWorldData(const FWorldEngineCreateRequest& InWorldData)
{
    WorldData = InWorldData;
}

FString UCreateWorldEngineRequest::GetVerb() const
{
    return TEXT("POST");
}

FString UCreateWorldEngineRequest::GetEndpoint() const
{
    return TEXT("/api/world-engine/create");
}

FString UCreateWorldEngineRequest::GetRequestBody() const
{
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    
    JsonObject->SetStringField(TEXT("world_id"), WorldData.WorldId);
    JsonObject->SetBoolField(TEXT("verbose"), WorldData.bVerbose);
    
    // Add rules array
    TArray<TSharedPtr<FJsonValue>> RulesArray;
    for (const FString& Rule : WorldData.Rules)
    {
        RulesArray.Add(MakeShareable(new FJsonValueString(Rule)));
    }
    JsonObject->SetArrayField(TEXT("rules"), RulesArray);
    
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);
    
    return OutputString;
}

void UCreateWorldEngineRequest::ProcessResponse(const FString& Response, bool bWasSuccessful, int32 StatusCode)
{
    Super::ProcessResponse(Response, bWasSuccessful, StatusCode);
    
    if (bWasSuccessful && StatusCode == 201)
    {
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);
        
        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            CreatedWorldState.WorldId = JsonObject->GetStringField(TEXT("world_id"));
            CreatedWorldState.TurnCount = JsonObject->GetIntegerField(TEXT("turn_count"));
            CreatedWorldState.RuleCount = JsonObject->GetIntegerField(TEXT("rule_count"));
            
            UE_LOG(LogAgentBridge, Log, TEXT("Successfully created world engine: %s"), *CreatedWorldState.WorldId);
        }
        else
        {
            UE_LOG(LogAgentBridge, Error, TEXT("Failed to parse world engine creation response"));
        }
    }
}

FWorldEngineState UCreateWorldEngineRequest::GetCreatedWorldState() const
{
    return CreatedWorldState;
}
