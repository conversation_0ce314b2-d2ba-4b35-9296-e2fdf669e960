// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Interfaces/IHttpRequest.h"
#include "Interfaces/IHttpResponse.h"
#include "WebServiceSubsystem.generated.h"

class UBaseApiRequest;

/**
 * Delegate for HTTP request completion (Blueprint-assignable event)
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnHttpRequestCompleted, bool, bWasSuccessful, int32, StatusCode, const FString&, Response);

/**
 * Delegate for login completion (Blueprint-callable)
 */
DECLARE_DYNAMIC_DELEGATE_ThreeParams(FOnLoginCompleted, bool, bWasSuccessful, int32, StatusCode, const FString&, Response);

/**
 * Delegate for HTTP request completion (Blueprint-callable)
 */
DECLARE_DYNAMIC_DELEGATE_ThreeParams(FOnHttpRequestCompletedDelegate, bool, bWasSuccessful, int32, StatusCode, const FString&, Response);

/**
 * Internal delegate for HTTP request completion (C++ only)
 */
DECLARE_DELEGATE_ThreeParams(FOnHttpRequestCompletedInternal, bool bWasSuccessful, int32 StatusCode, const FString& Response);

/**
 * Subsystem that handles user authentication and HTTP requests to the backend
 */
UCLASS()
class AGENTBRIDGE_API UWebServiceSubsystem : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    // Begin USubsystem
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    // End USubsystem

    /**
     * Login to the backend service
     * @param Username - The username to login with
     * @param Password - The password to login with
     * @param OnComplete - Delegate called when login completes
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Authentication")
    void Login(const FString& Username, const FString& Password, FOnHttpRequestCompletedDelegate OnComplete);

    /**
     * Event dispatched when a login request completes
     */
    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|Authentication")
    FOnHttpRequestCompleted OnLoginCompleted;

    /**
     * Logout from the backend service
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Authentication")
    void Logout();

    /**
     * Check if the user is currently logged in
     * @return True if logged in, false otherwise
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|Authentication")
    bool IsLoggedIn() const;

    /**
     * Get the current user's authentication token
     * @return The authentication token
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|Authentication")
    FString GetCurrentUserToken() const;

    /**
     * Get the current user's ID
     * @return The user ID
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|Authentication")
    FString GetCurrentUserId() const;

    /**
     * Execute an HTTP request
     * @param Request - The HTTP request to execute
     * @param OnComplete - Delegate called when the request completes
     * @return The HTTP request reference
     */
    TSharedRef<IHttpRequest, ESPMode::ThreadSafe> ExecuteHttpRequest(TSharedRef<IHttpRequest, ESPMode::ThreadSafe> Request, FOnHttpRequestCompletedInternal OnComplete);

    /**
     * Execute an HTTP request (Blueprint version)
     * @param WorldContextObject - The world context object
     * @param URL - The URL to send the request to
     * @param Verb - The HTTP verb (GET, POST, etc.)
     * @param ContentType - The content type header
     * @param Body - The request body
     * @param OnComplete - Delegate called when the request completes
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|API", meta = (DisplayName = "Execute HTTP Request"))
    void ExecuteHttpRequestBP(UObject* WorldContextObject, FString URL, FString Verb, FString ContentType, FString Body, FOnHttpRequestCompletedDelegate OnComplete);

    /**
     * Execute an API request
     * @param ApiRequest - The API request object to execute
     * @return True if the request was sent, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|API")
    bool ExecuteApiRequest(UBaseApiRequest* ApiRequest);

    /**
     * Get the base URL for the backend API
     * @return The base URL
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|API")
    FString GetBaseUrl() const;

    /**
     * Set the base URL for the backend API
     * @param NewBaseUrl - The new base URL
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|API")
    void SetBaseUrl(const FString& NewBaseUrl);

    /**
     * Check if verbose logging is enabled.
     * @return True if verbose logging is enabled, false otherwise.
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|Logging")
    bool IsVerboseLoggingEnabled() const { return bVerboseLogging; }

private:
    // The base URL for the backend API
    UPROPERTY()
    FString BaseUrl;

    // The current user's authentication token
    UPROPERTY()
    FString AuthToken;

    // The current user's ID
    UPROPERTY()
    FString UserId;

    // Whether to enable verbose logging
    UPROPERTY()
    bool bVerboseLogging;

    // Handle HTTP response
    void OnHttpResponseReceived(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FOnHttpRequestCompletedInternal OnComplete, int64 StartTimeTicks);
};
