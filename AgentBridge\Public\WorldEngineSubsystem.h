// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Models/WorldEngineModels.h"
#include "WorldEngineSubsystem.generated.h"

class UWebServiceSubsystem;
class UCreateWorldEngineRequest;
class UExecuteTurnRequest;

/**
 * Delegate for world engine creation completion
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnWorldEngineCreated, bool, bSuccess, const FWorldEngineState&, WorldState);

/**
 * Delegate for turn execution completion
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTurnExecuted, bool, bSuccess, const FTurnExecutionResponse&, TurnResponse);

/**
 * Subsystem for managing world engine operations
 */
UCLASS()
class AGENTBRIDGE_API UWorldEngineSubsystem : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem implementation
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    /**
     * Create a new world engine
     * @param WorldData - The world engine creation data
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|WorldEngine")
    void CreateWorldEngine(const FWorldEngineCreateRequest& WorldData);

    /**
     * Execute a turn in the world simulation
     * @param WorldId - The world identifier
     * @param TurnData - The turn execution data
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|WorldEngine")
    void ExecuteTurn(const FString& WorldId, const FTurnExecutionRequest& TurnData);

    /**
     * Get the current world engine state
     * @return The current world state
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|WorldEngine")
    FWorldEngineState GetCurrentWorldState() const { return CurrentWorldState; }

    /**
     * Get the last turn execution response
     * @return The last turn response
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|WorldEngine")
    FTurnExecutionResponse GetLastTurnResponse() const { return LastTurnResponse; }

    /**
     * Check if a world engine is currently active
     * @return True if a world is active
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|WorldEngine")
    bool IsWorldActive() const { return !CurrentWorldState.WorldId.IsEmpty(); }

    // Events
    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|WorldEngine|Events")
    FOnWorldEngineCreated OnWorldEngineCreated;

    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|WorldEngine|Events")
    FOnTurnExecuted OnTurnExecuted;

private:
    // Handle world creation response
    void HandleCreateWorldResponse(bool bSuccess, int32 StatusCode, const FString& Response);

    // Handle turn execution response
    void HandleExecuteTurnResponse(bool bSuccess, int32 StatusCode, const FString& Response);

    // Web service subsystem reference
    UPROPERTY()
    UWebServiceSubsystem* WebServiceSubsystem;

    // Current world state
    UPROPERTY()
    FWorldEngineState CurrentWorldState;

    // Last turn execution response
    UPROPERTY()
    FTurnExecutionResponse LastTurnResponse;

    // Active requests
    UPROPERTY()
    UCreateWorldEngineRequest* ActiveCreateRequest;

    UPROPERTY()
    UExecuteTurnRequest* ActiveTurnRequest;
};
