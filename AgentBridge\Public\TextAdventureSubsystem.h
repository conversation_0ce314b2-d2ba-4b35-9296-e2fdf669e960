// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Models/TextAdventureModels.h"
#include "TextAdventureSubsystem.generated.h"

class UWebServiceSubsystem;

/**
 * Delegate for text adventure setup completion
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTextAdventureSetup, bool, bSuccess, const FTextAdventureSetupResponse&, SetupResponse);

/**
 * Delegate for narrator creation completion
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnNarratorCreated, bool, bSuccess, const FNarratorState&, NarratorState);

/**
 * Delegate for chapter start completion
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnChapterStarted, bool, bSuccess, const FString&, InitialScene);

/**
 * Delegate for user action processing completion
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnUserActionProcessed, bool, bSuccess, const FTextAdventureResponse&, Response);

/**
 * Subsystem for managing text adventure operations
 */
UCLASS()
class AGENTBRIDGE_API UTextAdventureSubsystem : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem implementation
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    /**
     * Set up a complete text adventure with narrator, world, and initial chapter
     * @param SetupData - The text adventure setup data
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|TextAdventure")
    void SetupTextAdventure(const FTextAdventureSetupRequest& SetupData);

    /**
     * Create a new narrator engine
     * @param NarratorData - The narrator creation data
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|TextAdventure")
    void CreateNarrator(const FNarratorCreateRequest& NarratorData);

    /**
     * Add a chapter to the current narrator
     * @param ChapterData - The chapter configuration
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|TextAdventure")
    void AddChapter(const FChapterConfig& ChapterData);

    /**
     * Start a chapter
     * @param ChapterId - The chapter identifier to start
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|TextAdventure")
    void StartChapter(const FString& ChapterId);

    /**
     * Process a user action in the text adventure
     * @param WorldId - The world identifier
     * @param ActionData - The user action data
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|TextAdventure")
    void ProcessUserAction(const FString& WorldId, const FUserActionRequest& ActionData);

    /**
     * Get the current narrator state
     * @return The current narrator state
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|TextAdventure")
    FNarratorState GetCurrentNarratorState() const { return CurrentNarratorState; }

    /**
     * Get the last text adventure response
     * @return The last response
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|TextAdventure")
    FTextAdventureResponse GetLastResponse() const { return LastResponse; }

    /**
     * Check if a text adventure is currently active
     * @return True if active
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|TextAdventure")
    bool IsAdventureActive() const { return !CurrentNarratorState.NarratorId.IsEmpty() && CurrentNarratorState.bHasActiveChapter; }

    /**
     * Get the current narrator ID
     * @return The narrator ID
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|TextAdventure")
    FString GetCurrentNarratorId() const { return CurrentNarratorState.NarratorId; }

    /**
     * Get the current world ID
     * @return The world ID
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|TextAdventure")
    FString GetCurrentWorldId() const { return CurrentWorldId; }

    // Events
    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|TextAdventure|Events")
    FOnTextAdventureSetup OnTextAdventureSetup;

    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|TextAdventure|Events")
    FOnNarratorCreated OnNarratorCreated;

    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|TextAdventure|Events")
    FOnChapterStarted OnChapterStarted;

    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|TextAdventure|Events")
    FOnUserActionProcessed OnUserActionProcessed;

private:
    // Handle text adventure setup response
    void HandleSetupResponse(bool bSuccess, int32 StatusCode, const FString& Response);

    // Handle narrator creation response
    void HandleCreateNarratorResponse(bool bSuccess, int32 StatusCode, const FString& Response);

    // Handle chapter add response
    void HandleAddChapterResponse(bool bSuccess, int32 StatusCode, const FString& Response);

    // Handle chapter start response
    void HandleStartChapterResponse(bool bSuccess, int32 StatusCode, const FString& Response);

    // Handle user action response
    void HandleUserActionResponse(bool bSuccess, int32 StatusCode, const FString& Response);

    // Web service subsystem reference
    UPROPERTY()
    UWebServiceSubsystem* WebServiceSubsystem;

    // Current narrator state
    UPROPERTY()
    FNarratorState CurrentNarratorState;

    // Last text adventure response
    UPROPERTY()
    FTextAdventureResponse LastResponse;

    // Current world ID (tracked separately since narrator doesn't store it)
    UPROPERTY()
    FString CurrentWorldId;
};
