// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Interfaces/IHttpRequest.h"
#include "BaseApiRequest.generated.h"

class UWebServiceSubsystem;

/**
 * Delegate for API request completion
 * Using regular multicast delegate to support lambda functions
 */
DECLARE_MULTICAST_DELEGATE_ThreeParams(FOnApiRequestCompleted, bool /*bWasSuccessful*/, int32 /*StatusCode*/, const FString& /*Response*/);

/**
 * Dynamic delegate for Blueprint compatibility
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnApiRequestCompletedDynamic, bool, bWasSuccessful, int32, StatusCode, const FString&, Response);

/**
 * Base class for all API requests
 */
UCLASS(Abstract, BlueprintType, Blueprintable)
class AGENTBRIDGE_API UBaseApiRequest : public UObject
{
    GENERATED_BODY()

public:
    UBaseApiRequest();

    /**
     * Execute the API request
     * @param Subsystem - The web service subsystem to use
     * @return True if the request was sent, false otherwise
     */
    virtual bool Execute(UWebServiceSubsystem* Subsystem);

    /**
     * Get the HTTP verb for this request (GET, POST, etc.)
     * @return The HTTP verb
     */
    virtual FString GetVerb() const;

    /**
     * Get the endpoint for this request (e.g., "/agents")
     * @return The endpoint
     */
    virtual FString GetEndpoint() const;

    /**
     * Get the request body as a JSON string
     * @return The request body
     */
    virtual FString GetRequestBody() const;

    /**
     * Process the response from the server
     * @param Response - The response string
     * @param bWasSuccessful - Whether the request was successful
     * @param StatusCode - The HTTP status code
     */
    virtual void ProcessResponse(const FString& Response, bool bWasSuccessful, int32 StatusCode);

    /**
     * Event called when the request completes (C++ lambda support)
     */
    FOnApiRequestCompleted OnRequestCompleted;

    /**
     * Event called when the request completes (Blueprint support)
     */
    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|API")
    FOnApiRequestCompletedDynamic OnRequestCompletedDynamic;

protected:
    // The HTTP request object
    TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> HttpRequest;

private:
    // Handle HTTP response
    void OnHttpResponseReceived(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful);
};
