# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
ENV/
env/
.venv/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
*.sqlite3
*.db
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Documentation
docs/_build/
site/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Logs
logs/
*.log

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Development
.env.local
.env.development
.env.test
backup-*.tar.gz

# Testing
htmlcov/
.coverage
.pytest_cache/
test-results/

# Build artifacts
build/
dist/
*.egg-info/
