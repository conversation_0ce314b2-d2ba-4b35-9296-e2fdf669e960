#!/usr/bin/env python3
"""
Test script for the enhanced text adventure system.

This script demonstrates the complete text adventure game loop:
1. Setup narrator engine and world engine
2. Create agents
3. Initialize a chapter
4. Process user actions through the game loop
"""

import asyncio
import json
import sys
import os

# Add the src directory to the path - adjust for backend location
backend_path = r"C:\Users\<USER>\Documents\GitHub\langchain_agents\src"
sys.path.insert(0, backend_path)

from services import narrator_service, world_engine_service, agent_service
from schemas.text_adventure_schemas import (
    TextAdventureSetupRequest,
    ChapterCreate,
    UserActionRequest
)


async def test_text_adventure():
    """Test the complete text adventure system."""
    print("🎮 Testing Enhanced Text Adventure System")
    print("=" * 50)

    # Test configuration
    narrator_id = "test-narrator"
    world_id = "test-world"
    user_id = "test-user"

    # Step 1: Setup the text adventure
    print("\n📚 Step 1: Setting up text adventure...")

    chapter_data = ChapterCreate(
        chapter_id="chapter-1",
        title="The Mysterious Tavern",
        system_prompt="""You are a fantasy adventure narrator. Your style is immersive and descriptive,
        focusing on atmosphere and character interactions. You describe scenes with rich sensory details
        and maintain a sense of mystery and adventure. Keep responses engaging and leave room for player choice.""",
        initial_scene="""You find yourself standing outside 'The Prancing Pony', a weathered tavern
        nestled at the crossroads of two ancient paths. Warm light spills from its windows, and you can
        hear the murmur of conversation and clinking of tankards from within. The evening mist swirls
        around your feet as you consider your next move.""",
        objectives=[
            "Explore the tavern and meet its inhabitants",
            "Discover the source of the mysterious rumors",
            "Make allies or enemies based on your choices"
        ]
    )

    setup_request = TextAdventureSetupRequest(
        narrator_id=narrator_id,
        world_id=world_id,
        chapter=chapter_data,
        world_rules=[
            "Magic exists but is rare and mysterious",
            "The tavern is a neutral ground where violence is forbidden",
            "Information can be traded for coin or favors",
            "Some patrons may not be what they seem",
            "The weather affects travel and mood"
        ],
        agent_ids=[]  # We'll create agents separately
    )

    # Create narrator engine
    narrator_data = narrator_service.create_narrator_engine(
        narrator_id=narrator_id,
        verbose=True,
        user_id=user_id
    )
    print(f"✅ Created narrator engine: {narrator_data['narrator_id']}")

    # Create world engine
    world_data = world_engine_service.create_world_engine(
        world_id=world_id,
        rules=setup_request.world_rules,
        verbose=True,
        user_id=user_id
    )
    print(f"✅ Created world engine: {world_data['world_id']}")

    # Add chapter to narrator
    chapter_success = narrator_service.create_and_add_chapter(
        narrator_id=narrator_id,
        chapter_data=chapter_data.dict(),
        user_id=user_id
    )
    print(f"✅ Added chapter: {chapter_success}")

    # Start the chapter
    start_result = narrator_service.start_chapter(
        narrator_id=narrator_id,
        chapter_id=chapter_data.chapter_id,
        user_id=user_id
    )
    print(f"✅ Started chapter: {start_result['success']}")
    print(f"📖 Initial scene: {start_result.get('initial_scene', 'N/A')}")

    # Step 2: Create some NPCs
    print("\n👥 Step 2: Creating NPCs...")

    # Create a bartender
    bartender_data = agent_service.create_agent(
        agent_id="bartender-tom",
        name="Tom the Bartender",
        age=45,
        traits="Friendly, observant, knows everyone's business, protective of his tavern",
        status="Wiping down glasses behind the bar, keeping an eye on the patrons",
        location="Behind the bar",
        user_id=user_id
    )
    print(f"✅ Created agent: {bartender_data['name']}")

    # Create a mysterious stranger
    stranger_data = agent_service.create_agent(
        agent_id="mysterious-stranger",
        name="Hooded Stranger",
        age=None,
        traits="Secretive, watchful, speaks in riddles, seems to know more than they let on",
        status="Sitting alone in a dark corner, nursing a drink and watching the room",
        location="Corner table near the fireplace",
        user_id=user_id
    )
    print(f"✅ Created agent: {stranger_data['name']}")

    # Create a local merchant
    merchant_data = agent_service.create_agent(
        agent_id="merchant-elena",
        name="Elena the Merchant",
        age=35,
        traits="Talkative, well-traveled, loves gossip, always looking for a good deal",
        status="Chatting loudly with other patrons about her recent travels",
        location="Center table with other travelers",
        user_id=user_id
    )
    print(f"✅ Created agent: {merchant_data['name']}")

    agent_ids = ["bartender-tom", "mysterious-stranger", "merchant-elena"]

    # Step 3: Test the game loop with user actions
    print("\n🎯 Step 3: Testing game loop...")

    test_actions = [
        {
            "action": "I push open the heavy wooden door and step into the tavern, looking around to get my bearings",
            "location": "Tavern entrance"
        },
        {
            "action": "I approach the bar and nod to the bartender, ordering a pint of ale",
            "location": "At the bar"
        },
        {
            "action": "I glance over at the hooded figure in the corner, trying to get a better look at them",
            "location": "At the bar"
        }
    ]

    for i, test_action in enumerate(test_actions, 1):
        print(f"\n--- Turn {i} ---")
        print(f"🎭 User Action: {test_action['action']}")
        print(f"📍 User Location: {test_action['location']}")

        # Process the user action
        action_request = UserActionRequest(
            user_action=test_action['action'],
            user_location=test_action['location'],
            agent_ids=agent_ids
        )

        result = narrator_service.process_user_action(
            narrator_id=narrator_id,
            world_id=world_id,
            user_action=action_request.user_action,
            user_location=action_request.user_location,
            agent_ids=action_request.agent_ids,
            user_id=user_id
        )

        if result:
            print(f"\n✅ Action Evaluation: {'Possible' if result['action_evaluation']['is_possible'] else 'Impossible'}")
            print(f"   Reason: {result['action_evaluation']['reason']}")

            print(f"\n👀 Observing Agents: {result['observing_agents']}")

            if result['agent_reactions']:
                print("\n🎭 Agent Reactions:")
                for reaction in result['agent_reactions']:
                    delay_info = f" (noticed after {reaction.get('delay_seconds', 0)}s)" if reaction.get('delay_seconds', 0) > 0 else ""
                    print(f"   • {reaction['agent_name']}{delay_info}:")
                    print(f"     Observed: {reaction['observation']}")
                    print(f"     Reaction: {reaction['action']}")
                    print(f"     Dialogue: {'Yes' if reaction['is_dialogue'] else 'No'}")

            print(f"\n📖 Narration:")
            print(f"   {result['narration']}")

            print(f"\n🌍 World Turn: {result['world_turn']}")
        else:
            print("❌ Failed to process user action")

        print("-" * 50)

    print("\n🎉 Text adventure test completed!")
    print("The enhanced system successfully:")
    print("✅ Evaluated user action feasibility")
    print("✅ Found observing agents in chronological order")
    print("✅ Generated agent reactions with proper memory handling")
    print("✅ Created rich narrative responses")
    print("✅ Maintained world state and turn progression")


if __name__ == "__main__":
    asyncio.run(test_text_adventure())
