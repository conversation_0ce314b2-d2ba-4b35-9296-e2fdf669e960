// Copyright Epic Games, Inc. All Rights Reserved.

#include "Config/WorldInstanceConfigManager.h"
#include "Config/ConfigurationManager.h"
#include "Models/ConfigurationModels.h"

DEFINE_LOG_CATEGORY_STATIC(LogWorldInstanceConfigManager, Log, All);

void UWorldInstanceConfigManager::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    UE_LOG(LogWorldInstanceConfigManager, Log, TEXT("WorldInstanceConfigManager Initialized."));
    
    // Bind to the unified configuration manager's delegate
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (ConfigManager)
    {
        ConfigManager->OnAllConfigsLoaded.AddDynamic(this, &UWorldInstanceConfigManager::OnUnifiedConfigsLoaded);
    }
}

void UWorldInstanceConfigManager::Deinitialize()
{
    UE_LOG(LogWorldInstanceConfigManager, Log, TEXT("WorldInstanceConfigManager Deinitialized."));
    
    // Unbind from the unified configuration manager's delegate
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (ConfigManager)
    {
        ConfigManager->OnAllConfigsLoaded.RemoveDynamic(this, &UWorldInstanceConfigManager::OnUnifiedConfigsLoaded);
    }
    
    Super::Deinitialize();
}

UConfigurationManager* UWorldInstanceConfigManager::GetConfigurationManager() const
{
    return GetGameInstance()->GetSubsystem<UConfigurationManager>();
}

void UWorldInstanceConfigManager::LoadAllWorldInstanceConfigurationsRecursive(const FString& OptionalPathInRepo, int32 MaxRecursionDepth)
{
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (ConfigManager)
    {
        UE_LOG(LogWorldInstanceConfigManager, Log, TEXT("Loading world instance configurations via unified system..."));
        ConfigManager->LoadAllConfigurationsRecursive(EConfigurationType::WorldInstance, OptionalPathInRepo, MaxRecursionDepth);
    }
    else
    {
        UE_LOG(LogWorldInstanceConfigManager, Error, TEXT("Failed to get ConfigurationManager subsystem"));
        OnAllWorldInstanceConfigsLoaded.Broadcast(false);
    }
}

void UWorldInstanceConfigManager::GetFileList(const FString& PathInRepo, FWorldInstanceFileListResponseDelegate CompletionDelegate)
{
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (ConfigManager)
    {
        // Convert our delegate to the unified system's delegate
        FConfigFileListResponseDelegate UnifiedDelegate;
        UnifiedDelegate.BindUFunction(this, FName("OnFileListReceived"));
        
        // Store the original delegate for callback
        // Note: In a full implementation, you'd want to store this mapping properly
        ConfigManager->GetFileList(EConfigurationType::WorldInstance, PathInRepo, UnifiedDelegate);
    }
    else
    {
        UE_LOG(LogWorldInstanceConfigManager, Error, TEXT("Failed to get ConfigurationManager subsystem"));
        CompletionDelegate.ExecuteIfBound(false, PathInRepo, {}, {});
    }
}

void UWorldInstanceConfigManager::FetchAndCacheWorldInstanceConfig(const FString& FilePathInRepo, FWorldInstanceFileContentResponseDelegate CompletionDelegate)
{
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (ConfigManager)
    {
        // Convert our delegate to the unified system's delegate
        FConfigFileContentResponseDelegate UnifiedDelegate;
        UnifiedDelegate.BindUFunction(this, FName("OnFileContentReceived"));
        
        // Store the original delegate for callback
        // Note: In a full implementation, you'd want to store this mapping properly
        ConfigManager->FetchAndCacheConfig(EConfigurationType::WorldInstance, FilePathInRepo, UnifiedDelegate);
    }
    else
    {
        UE_LOG(LogWorldInstanceConfigManager, Error, TEXT("Failed to get ConfigurationManager subsystem"));
        CompletionDelegate.ExecuteIfBound(false, FilePathInRepo, TEXT(""));
    }
}

bool UWorldInstanceConfigManager::GetWorldInstanceConfiguration(const FString& ConfigFilePathInRepo, FWorldInstanceConfig& OutConfig) const
{
    return GetConfiguration<FWorldInstanceConfig>(ConfigFilePathInRepo, OutConfig);
}

bool UWorldInstanceConfigManager::GetWorldInstanceConfigurationById(const FString& WorldId, FWorldInstanceConfig& OutConfig) const
{
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (!ConfigManager)
    {
        return false;
    }

    // Search through all cached world instance configurations to find one with the matching WorldId
    const TMap<FString, FString>& WorldInstanceCache = ConfigManager->GetRawConfigCache(EConfigurationType::WorldInstance);
    for (const TPair<FString, FString>& ConfigPair : WorldInstanceCache)
    {
        FWorldInstanceConfig TempConfig;
        if (ConfigManager->GetConfiguration(EConfigurationType::WorldInstance, ConfigPair.Key, TempConfig))
        {
            if (TempConfig.WorldId.Equals(WorldId, ESearchCase::IgnoreCase))
            {
                OutConfig = TempConfig;
                return true;
            }
        }
    }

    UE_LOG(LogWorldInstanceConfigManager, Warning, TEXT("World instance configuration with ID '%s' not found in cache."), *WorldId);
    return false;
}

const TMap<FString, FString>& UWorldInstanceConfigManager::GetRawWorldInstanceConfigCache() const
{
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (ConfigManager)
    {
        return ConfigManager->GetRawConfigCache(EConfigurationType::WorldInstance);
    }
    
    // Return empty map if configuration manager not available
    static TMap<FString, FString> EmptyMap;
    return EmptyMap;
}

TArray<FString> UWorldInstanceConfigManager::GetAvailableWorldInstanceIds() const
{
    TArray<FString> WorldInstanceIds;
    
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (!ConfigManager)
    {
        return WorldInstanceIds;
    }

    // Iterate through all raw JSON configurations
    const TMap<FString, FString>& WorldInstanceCache = ConfigManager->GetRawConfigCache(EConfigurationType::WorldInstance);
    for (const TPair<FString, FString>& ConfigPair : WorldInstanceCache)
    {
        FWorldInstanceConfig WorldInstanceConfig;
        if (ConfigManager->GetConfiguration(EConfigurationType::WorldInstance, ConfigPair.Key, WorldInstanceConfig))
        {
            WorldInstanceIds.Add(WorldInstanceConfig.WorldId);
        }
    }

    return WorldInstanceIds;
}

void UWorldInstanceConfigManager::OnUnifiedConfigsLoaded(EConfigurationType ConfigType, bool bSuccess)
{
    // Only respond to world instance configuration events
    if (ConfigType == EConfigurationType::WorldInstance)
    {
        UE_LOG(LogWorldInstanceConfigManager, Log, TEXT("World instance configurations loaded: %s"), bSuccess ? TEXT("Success") : TEXT("Failed"));
        OnAllWorldInstanceConfigsLoaded.Broadcast(bSuccess);
    }
}
