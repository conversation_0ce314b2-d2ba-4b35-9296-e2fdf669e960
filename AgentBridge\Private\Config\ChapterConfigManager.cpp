// Copyright Epic Games, Inc. All Rights Reserved.

#include "Config/ChapterConfigManager.h"
#include "Config/ConfigurationManager.h"
#include "Models/TextAdventureModels.h"

DEFINE_LOG_CATEGORY_STATIC(LogChapterConfigManager, Log, All);

void UChapterConfigManager::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    UE_LOG(LogChapterConfigManager, Log, TEXT("ChapterConfigManager Initialized."));

    // Bind to the unified configuration manager's delegate
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (ConfigManager)
    {
        ConfigManager->OnAllConfigsLoaded.AddDynamic(this, &UChapterConfigManager::OnUnifiedConfigsLoaded);
    }
}

void UChapterConfigManager::Deinitialize()
{
    UE_LOG(LogChapterConfigManager, Log, TEXT("ChapterConfigManager Deinitialized."));

    // Unbind from the unified configuration manager's delegate
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (ConfigManager)
    {
        ConfigManager->OnAllConfigsLoaded.RemoveDynamic(this, &UChapterConfigManager::OnUnifiedConfigsLoaded);
    }

    Super::Deinitialize();
}

UConfigurationManager* UChapterConfigManager::GetConfigurationManager() const
{
    return GetGameInstance()->GetSubsystem<UConfigurationManager>();
}

void UChapterConfigManager::LoadAllChapterConfigurationsRecursive(const FString& OptionalPathInRepo, int32 MaxRecursionDepth)
{
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (ConfigManager)
    {
        UE_LOG(LogChapterConfigManager, Log, TEXT("Loading chapter configurations via unified system..."));
        ConfigManager->LoadAllConfigurationsRecursive(EConfigurationType::Chapter, OptionalPathInRepo, MaxRecursionDepth);
    }
    else
    {
        UE_LOG(LogChapterConfigManager, Error, TEXT("Failed to get ConfigurationManager subsystem"));
        OnAllChapterConfigsLoaded.Broadcast(false);
    }
}

void UChapterConfigManager::GetFileList(const FString& PathInRepo, FChapterFileListResponseDelegate CompletionDelegate)
{
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (ConfigManager)
    {
        // Convert our delegate to the unified system's delegate
        FConfigFileListResponseDelegate UnifiedDelegate;
        UnifiedDelegate.BindUFunction(this, FName("OnFileListReceived"));

        // Store the original delegate for callback
        // Note: In a full implementation, you'd want to store this mapping properly
        ConfigManager->GetFileList(EConfigurationType::Chapter, PathInRepo, UnifiedDelegate);
    }
    else
    {
        UE_LOG(LogChapterConfigManager, Error, TEXT("Failed to get ConfigurationManager subsystem"));
        CompletionDelegate.ExecuteIfBound(false, PathInRepo, {}, {});
    }
}

void UChapterConfigManager::FetchAndCacheChapterConfig(const FString& FilePathInRepo, FChapterFileContentResponseDelegate CompletionDelegate)
{
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (ConfigManager)
    {
        // Convert our delegate to the unified system's delegate
        FConfigFileContentResponseDelegate UnifiedDelegate;
        UnifiedDelegate.BindUFunction(this, FName("OnFileContentReceived"));

        // Store the original delegate for callback
        // Note: In a full implementation, you'd want to store this mapping properly
        ConfigManager->FetchAndCacheConfig(EConfigurationType::Chapter, FilePathInRepo, UnifiedDelegate);
    }
    else
    {
        UE_LOG(LogChapterConfigManager, Error, TEXT("Failed to get ConfigurationManager subsystem"));
        CompletionDelegate.ExecuteIfBound(false, FilePathInRepo, TEXT(""));
    }
}

bool UChapterConfigManager::GetChapterConfiguration(const FString& ConfigFilePathInRepo, FChapterConfig& OutConfig) const
{
    return GetConfiguration<FChapterConfig>(ConfigFilePathInRepo, OutConfig);
}

bool UChapterConfigManager::GetChapterConfigurationById(const FString& ChapterId, FChapterConfig& OutConfig) const
{
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (!ConfigManager)
    {
        return false;
    }

    // Search through all cached chapter configurations to find one with the matching ChapterId
    const TMap<FString, FString>& ChapterCache = ConfigManager->GetRawConfigCache(EConfigurationType::Chapter);
    for (const TPair<FString, FString>& ConfigPair : ChapterCache)
    {
        FChapterConfig TempConfig;
        if (ConfigManager->GetConfiguration(EConfigurationType::Chapter, ConfigPair.Key, TempConfig))
        {
            if (TempConfig.ChapterId.Equals(ChapterId, ESearchCase::IgnoreCase))
            {
                OutConfig = TempConfig;
                return true;
            }
        }
    }

    UE_LOG(LogChapterConfigManager, Warning, TEXT("Chapter configuration with ID '%s' not found in cache."), *ChapterId);
    return false;
}

const TMap<FString, FString>& UChapterConfigManager::GetRawChapterConfigCache() const
{
    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (ConfigManager)
    {
        return ConfigManager->GetRawConfigCache(EConfigurationType::Chapter);
    }

    // Return empty map if configuration manager not available
    static TMap<FString, FString> EmptyMap;
    return EmptyMap;
}

TArray<FString> UChapterConfigManager::GetAvailableChapterIds() const
{
    TArray<FString> ChapterIds;

    UConfigurationManager* ConfigManager = GetConfigurationManager();
    if (!ConfigManager)
    {
        return ChapterIds;
    }

    // Iterate through all raw JSON configurations
    const TMap<FString, FString>& ChapterCache = ConfigManager->GetRawConfigCache(EConfigurationType::Chapter);
    for (const TPair<FString, FString>& ConfigPair : ChapterCache)
    {
        FChapterConfig ChapterConfig;
        if (ConfigManager->GetConfiguration(EConfigurationType::Chapter, ConfigPair.Key, ChapterConfig))
        {
            ChapterIds.Add(ChapterConfig.ChapterId);
        }
    }

    return ChapterIds;
}

void UChapterConfigManager::OnUnifiedConfigsLoaded(EConfigurationType ConfigType, bool bSuccess)
{
    // Only respond to chapter configuration events
    if (ConfigType == EConfigurationType::Chapter)
    {
        UE_LOG(LogChapterConfigManager, Log, TEXT("Chapter configurations loaded: %s"), bSuccess ? TEXT("Success") : TEXT("Failed"));
        OnAllChapterConfigsLoaded.Broadcast(bSuccess);
    }
}