"""
Narrator Engine implementation for managing narrative generation in text adventures.

This module provides the core functionality for:
- Managing chapters and their progression
- Generating narrative responses based on user actions and agent reactions
- Coordinating with the WorldEngine for game state
- Maintaining narrative consistency and style
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from langchain.chains import <PERSON><PERSON>hain
from langchain_core.language_models import BaseLanguageModel
from langchain_core.prompts import PromptTemplate

from .chapter import Chapter
from utils.llm_utils import get_llm

logger = logging.getLogger(__name__)


class NarratorEngine:
    """
    Manages narrative generation for text adventures.

    The NarratorEngine is responsible for:
    - Managing chapters and their progression
    - Generating narrative responses based on game events
    - Maintaining narrative consistency
    - Coordinating with WorldEngine for world state
    """

    def __init__(
        self,
        narrator_id: str,
        llm: Optional[BaseLanguageModel] = None,
        verbose: bool = False
    ):
        """
        Initialize the NarratorEngine.

        Args:
            narrator_id: Unique identifier for this narrator instance
            llm: Language model to use (defaults to system default)
            verbose: Enable verbose logging
        """
        self.narrator_id = narrator_id
        self.llm = llm or get_llm()
        self.verbose = verbose
        self.chapters: Dict[str, Chapter] = {}
        self.current_chapter: Optional[Chapter] = None
        self.narrative_history: List[Dict[str, Any]] = []

        logger.info(f"NarratorEngine initialized with ID: {narrator_id}")

    def add_chapter(self, chapter: Chapter) -> None:
        """
        Add a chapter to the narrator.

        Args:
            chapter: Chapter instance to add
        """
        self.chapters[chapter.chapter_id] = chapter
        logger.info(f"Added chapter '{chapter.title}' to narrator {self.narrator_id}")

    def start_chapter(self, chapter_id: str) -> str:
        """
        Start a specific chapter.

        Args:
            chapter_id: ID of the chapter to start

        Returns:
            Initial scene description

        Raises:
            ValueError: If chapter not found
        """
        if chapter_id not in self.chapters:
            raise ValueError(f"Chapter '{chapter_id}' not found")

        self.current_chapter = self.chapters[chapter_id]
        initial_scene = self.current_chapter.start()

        # Record in narrative history
        self.narrative_history.append({
            "type": "chapter_start",
            "chapter_id": chapter_id,
            "timestamp": datetime.now().isoformat(),
            "content": initial_scene
        })

        return initial_scene

    def generate_narration(
        self,
        user_action: str,
        agent_reactions: List[Dict[str, Any]],
        world_state: Dict[str, Any],
        action_evaluation: Dict[str, Any]
    ) -> str:
        """
        Generate narrative response based on user action and agent reactions.

        Args:
            user_action: The user's attempted action
            agent_reactions: List of agent reactions with their observations
            world_state: Current world state from WorldEngine
            action_evaluation: Evaluation of action feasibility

        Returns:
            Narrative text describing the outcome
        """
        if not self.current_chapter:
            raise ValueError("No chapter is currently active")

        # Increment turn count
        self.current_chapter.increment_turn()

        # Prepare agent reactions context
        reactions_context = self._format_agent_reactions(agent_reactions)

        # Get chapter context
        chapter_context = self.current_chapter.get_narration_context()

        # Create the narration prompt
        prompt = PromptTemplate.from_template(
            """{system_prompt}

Chapter: {chapter_title}
Turn: {turn_count}

User Action: {user_action}
Action Feasibility: {action_feasibility}
{feasibility_reason}

Agent Observations and Reactions:
{agent_reactions}

World State Summary:
{world_state_summary}

Chapter Objectives:
{objectives}

Recent Narrative Context:
{recent_narrative}

Based on the above information, generate a narrative response that:
1. Describes the outcome of the user's action
2. Incorporates the agent reactions naturally into the narrative
3. Maintains consistency with the chapter's tone and style
4. Advances the story toward the chapter objectives
5. Provides clear sensory details and atmosphere
6. Ends with a clear situation for the player to respond to

Narrative Response:"""
        )

        # Get recent narrative for context
        recent_narrative = self._get_recent_narrative(3)

        # Format feasibility information
        feasibility_str = "POSSIBLE" if action_evaluation.get("is_possible", True) else "IMPOSSIBLE"
        feasibility_reason = ""
        if not action_evaluation.get("is_possible", True):
            feasibility_reason = f"Reason: {action_evaluation.get('reason', 'Unknown')}"

        # Generate narration
        chain = LLMChain(llm=self.llm, prompt=prompt, verbose=self.verbose)

        narration = chain.run(
            system_prompt=self.current_chapter.system_prompt,
            chapter_title=self.current_chapter.title,
            turn_count=self.current_chapter.turn_count,
            user_action=user_action,
            action_feasibility=feasibility_str,
            feasibility_reason=feasibility_reason,
            agent_reactions=reactions_context,
            world_state_summary=self._format_world_state(world_state),
            objectives="\n".join([f"- {obj}" for obj in self.current_chapter.objectives]),
            recent_narrative=recent_narrative
        ).strip()

        # Record in narrative history
        self.narrative_history.append({
            "type": "narration",
            "chapter_id": self.current_chapter.chapter_id,
            "turn": self.current_chapter.turn_count,
            "timestamp": datetime.now().isoformat(),
            "user_action": user_action,
            "content": narration
        })

        logger.debug(f"Generated narration for turn {self.current_chapter.turn_count}")

        return narration

    def _format_agent_reactions(self, agent_reactions: List[Dict[str, Any]]) -> str:
        """
        Format agent reactions for the narration prompt.

        Args:
            agent_reactions: List of agent reaction dictionaries

        Returns:
            Formatted string of agent reactions
        """
        if not agent_reactions:
            return "No agents observed or reacted to this action."

        formatted_reactions = []
        for reaction in agent_reactions:
            agent_name = reaction.get("agent_name", "Unknown")
            observation = reaction.get("observation", "")
            action = reaction.get("action", "No reaction")

            formatted_reactions.append(
                f"{agent_name}:\n"
                f"  Observed: {observation}\n"
                f"  Reaction: {action}"
            )

        return "\n\n".join(formatted_reactions)

    def _format_world_state(self, world_state: Dict[str, Any]) -> str:
        """
        Format world state for the narration prompt.

        Args:
            world_state: World state dictionary

        Returns:
            Formatted world state summary
        """
        # Extract key information from world state
        turn_count = world_state.get("turn_count", 0)
        world_id = world_state.get("world_id", "Unknown")
        user_location = world_state.get("user_location", "Unknown")
        user_action = world_state.get("user_action", "Unknown")
        active_agents = world_state.get("active_agents", [])
        agent_locations = world_state.get("agent_locations", {})
        agent_statuses = world_state.get("agent_statuses", {})
        observing_agents_count = world_state.get("observing_agents_count", 0)
        action_feasible = world_state.get("action_feasible", True)

        summary_parts = [
            f"World: {world_id} (Turn {turn_count})",
            f"User Location: {user_location}",
            f"User Action: {user_action}",
            f"Action Feasibility: {'Possible' if action_feasible else 'Impossible'}",
            f"Active Agents: {len(active_agents)} ({observing_agents_count} observed the action)"
        ]

        # Add agent location information
        if agent_locations:
            summary_parts.append("\nAgent Locations:")
            for agent_name, location in agent_locations.items():
                status = agent_statuses.get(agent_name, "Unknown status")
                summary_parts.append(f"  - {agent_name}: {location} ({status})")

        return "\n".join(summary_parts)

    def _get_recent_narrative(self, count: int = 3) -> str:
        """
        Get recent narrative entries for context.

        Args:
            count: Number of recent entries to retrieve

        Returns:
            Formatted recent narrative
        """
        recent_entries = []

        # Get last 'count' narration entries
        narration_entries = [
            entry for entry in self.narrative_history
            if entry["type"] == "narration"
        ][-count:]

        for entry in narration_entries:
            recent_entries.append(
                f"Turn {entry.get('turn', '?')}: {entry.get('user_action', 'Unknown action')}\n"
                f"Result: {entry.get('content', '')[:200]}..."
            )

        return "\n\n".join(recent_entries) if recent_entries else "No previous narrative."

    def get_narrative_history(self) -> List[Dict[str, Any]]:
        """
        Get the complete narrative history.

        Returns:
            List of narrative history entries
        """
        return self.narrative_history

    def get_current_chapter_info(self) -> Optional[Dict[str, Any]]:
        """
        Get information about the current chapter.

        Returns:
            Dictionary with current chapter info or None
        """
        if not self.current_chapter:
            return None

        return self.current_chapter.to_dict()

    def complete_current_chapter(self) -> None:
        """Complete the current chapter."""
        if self.current_chapter:
            self.current_chapter.complete()
            logger.info(f"Completed chapter '{self.current_chapter.title}'")
