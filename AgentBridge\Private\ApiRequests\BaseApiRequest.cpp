// Copyright Epic Games, Inc. All Rights Reserved.

#include "ApiRequests/BaseApiRequest.h"
#include "WebServiceSubsystem.h"
#include "HttpModule.h"
#include "Interfaces/IHttpResponse.h"
#include "AgentBridge.h"

UBaseApiRequest::UBaseApiRequest()
{
}

bool UBaseApiRequest::Execute(UWebServiceSubsystem* Subsystem)
{
    if (!Subsystem)
    {
        UE_LOG(LogAgentBridge, Error, TEXT("UBaseApiRequest::Execute: Subsystem is null"));
        return false;
    }

    FString RequestType = GetClass()->GetName();
    FString Endpoint = GetEndpoint();
    FString Verb = GetVerb();

    UE_LOG(LogAgentBridge, Log, TEXT("Executing API request: Type=%s, Endpoint=%s, Verb=%s"),
        *RequestType, *Endpoint, *Verb);

    // Create the HTTP request
    HttpRequest = FHttpModule::Get().CreateRequest();
    HttpRequest->SetVerb(Verb);
    HttpRequest->SetURL(FString::Printf(TEXT("%s%s"), *Subsystem->GetBaseUrl(), *Endpoint));
    HttpRequest->SetHeader("Content-Type", "application/json");

    // Set the request body if needed
    FString RequestBody = GetRequestBody();
    if (!RequestBody.IsEmpty())
    {
        HttpRequest->SetContentAsString(RequestBody);

        // Log request body if it's not too long
        if (Subsystem->IsVerboseLoggingEnabled() && RequestBody.Len() <= 1000)
        {
            UE_LOG(LogAgentBridge, Log, TEXT("API Request Body: %s"), *RequestBody);
        }
        else if (Subsystem->IsVerboseLoggingEnabled())
        {
            UE_LOG(LogAgentBridge, Log, TEXT("API Request Body: [%d characters, too long to display]"), RequestBody.Len());
        }
    }

    // Execute the request
    FOnHttpRequestCompletedInternal OnComplete;
    // Use weak pointer to prevent accessing destroyed object
    TWeakObjectPtr<UBaseApiRequest> WeakThis(this);
    OnComplete.BindLambda([WeakThis, RequestType](bool bWasSuccessful, int32 StatusCode, const FString& Response)
    {
        // Check if the object is still valid before accessing it
        if (!WeakThis.IsValid())
        {
            UE_LOG(LogAgentBridge, Warning, TEXT("API request %s completed but object was destroyed, skipping response processing"), *RequestType);
            return;
        }

        UBaseApiRequest* StrongThis = WeakThis.Get();
        
        // Log response
        if (bWasSuccessful)
        {
            UE_LOG(LogAgentBridge, Log, TEXT("API request %s completed: Status=%d"), *RequestType, StatusCode);
        }
        else
        {
            UE_LOG(LogAgentBridge, Warning, TEXT("API request %s failed: Status=%d"), *RequestType, StatusCode);
        }

        // Process the response
        StrongThis->ProcessResponse(Response, bWasSuccessful, StatusCode);

        // Broadcast the response to both delegates
        if (StrongThis->OnRequestCompleted.IsBound())
        {
            StrongThis->OnRequestCompleted.Broadcast(bWasSuccessful, StatusCode, Response);
        }
        
        if (StrongThis->OnRequestCompletedDynamic.IsBound())
        {
            StrongThis->OnRequestCompletedDynamic.Broadcast(bWasSuccessful, StatusCode, Response);
        }
    });
    Subsystem->ExecuteHttpRequest(HttpRequest.ToSharedRef(), OnComplete);

    return true;
}

FString UBaseApiRequest::GetVerb() const
{
    // Default to GET, override in derived classes
    return TEXT("GET");
}

FString UBaseApiRequest::GetEndpoint() const
{
    // Must be overridden in derived classes
    return TEXT("");
}

FString UBaseApiRequest::GetRequestBody() const
{
    // Default to empty, override in derived classes
    return TEXT("");
}

void UBaseApiRequest::ProcessResponse(const FString& Response, bool bWasSuccessful, int32 StatusCode)
{
    // Default implementation logs the response but does nothing else
    // Derived classes should override this method to process the response

    if (bWasSuccessful)
    {
        UE_LOG(LogAgentBridge, Verbose, TEXT("Processing successful response for %s: Status=%d"),
            *GetClass()->GetName(), StatusCode);
    }
    else
    {
        UE_LOG(LogAgentBridge, Verbose, TEXT("Processing failed response for %s: Status=%d"),
            *GetClass()->GetName(), StatusCode);
    }
}

// This method is no longer needed as we're using a lambda in Execute() instead
// Keeping this as a stub for backward compatibility
void UBaseApiRequest::OnHttpResponseReceived(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful)
{
    // This method is deprecated - using lambda in Execute() instead
}
