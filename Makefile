# Makefile for Generative Agents API Development

.PHONY: help setup dev prod test clean logs shell format lint

# Default target
help:
	@echo "🚀 Generative Agents API Development Commands"
	@echo "=============================================="
	@echo ""
	@echo "Setup Commands:"
	@echo "  setup          - Initial setup of development environment"
	@echo "  build          - Build Docker images"
	@echo ""
	@echo "Development Commands:"
	@echo "  dev            - Start development server with hot reload"
	@echo "  dev-full       - Start dev server with Redis and tools"
	@echo "  prod           - Start production-like server for testing"
	@echo ""
	@echo "Testing Commands:"
	@echo "  test           - Run all tests"
	@echo "  test-cov       - Run tests with coverage"
	@echo "  test-timeout   - Test timeout fixes"
	@echo ""
	@echo "Utility Commands:"
	@echo "  logs           - Show application logs"
	@echo "  shell          - Open shell in development container"
	@echo "  redis-cli      - Open Redis CLI"
	@echo "  clean          - Clean up containers and volumes"
	@echo ""
	@echo "Code Quality Commands:"
	@echo "  format         - Format code with black and isort"
	@echo "  lint           - Run linting checks"
	@echo "  check          - Run all code quality checks"

# Setup commands
setup:
	@echo "🔧 Setting up development environment..."
	@chmod +x scripts/dev-setup.sh
	@./scripts/dev-setup.sh

build:
	@echo "🏗️  Building Docker images..."
	@docker-compose build

# Development commands
dev:
	@echo "🚀 Starting development server..."
	@docker-compose up api

dev-full:
	@echo "🚀 Starting development server with Redis and tools..."
	@docker-compose --profile tools up api redis redis-commander

prod:
	@echo "🏭 Starting production-like server..."
	@docker-compose --profile production up api-prod redis

# Testing commands
test:
	@echo "🧪 Running tests..."
	@docker-compose run --rm api pytest

test-cov:
	@echo "🧪 Running tests with coverage..."
	@docker-compose run --rm api pytest --cov=. --cov-report=html --cov-report=term

test-timeout:
	@echo "🕐 Testing timeout fixes..."
	@docker-compose run --rm api python test_timeout_fixes.py

# Utility commands
logs:
	@echo "📋 Showing application logs..."
	@docker-compose logs -f api

shell:
	@echo "🐚 Opening shell in development container..."
	@docker-compose run --rm api bash

redis-cli:
	@echo "🔴 Opening Redis CLI..."
	@docker-compose exec redis redis-cli

clean:
	@echo "🧹 Cleaning up containers and volumes..."
	@docker-compose down -v
	@docker system prune -f

# Code quality commands
format:
	@echo "🎨 Formatting code..."
	@docker-compose run --rm api black .
	@docker-compose run --rm api isort .

lint:
	@echo "🔍 Running linting checks..."
	@docker-compose run --rm api flake8 .
	@docker-compose run --rm api mypy . --ignore-missing-imports

check: format lint test
	@echo "✅ All code quality checks completed"

# Health check
health:
	@echo "🏥 Checking application health..."
	@curl -s http://localhost:5000/health | python -m json.tool

# Quick restart
restart:
	@echo "🔄 Restarting development server..."
	@docker-compose restart api

# View documentation
docs:
	@echo "📚 Opening API documentation..."
	@open http://localhost:5000/docs || start http://localhost:5000/docs || xdg-open http://localhost:5000/docs

# Database/Redis management
redis-flush:
	@echo "🗑️  Flushing Redis database..."
	@docker-compose exec redis redis-cli FLUSHALL

# Backup data
backup:
	@echo "💾 Creating data backup..."
	@tar -czf backup-$(shell date +%Y%m%d-%H%M%S).tar.gz data/

# Install dependencies locally (for IDE support)
install-local:
	@echo "📦 Installing dependencies locally..."
	@pip install -r requirements.txt
	@pip install -r requirements-dev.txt
