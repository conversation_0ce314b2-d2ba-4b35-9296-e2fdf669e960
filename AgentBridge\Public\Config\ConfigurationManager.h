// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Interfaces/IHttpRequest.h"
#include "Models/ConfigurationModels.h"
#include "ConfigurationManager.generated.h"

// Forward declarations
class UAgentBridgeConfigSettings;

// Delegates for asynchronous operations
DECLARE_DYNAMIC_DELEGATE_FourParams(FConfigFileListResponseDelegate, bool, bSuccess, const FString&, Path, const TArray<FString>&, FileNames, const TArray<FString>&, FolderNames);
DECLARE_DYNAMIC_DELEGATE_ThreeParams(FConfigFileContentResponseDelegate, bool, bSuccess, const FString&, FilePath, const FString&, JsonContent);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAllConfigsRequestCompleted, EConfigurationType, ConfigType, bool, bOverallSuccess);

/**
 * Unified Configuration Manager for loading and managing all types of configurations
 * from the GitHub repository. Supports Agent, Chapter, and World Instance configurations.
 */
UCLASS()
class AGENTBRIDGE_API UConfigurationManager : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    //~ Begin USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    //~ End USubsystem interface

    /**
     * Loads all configurations of a specific type starting from the default or specified path.
     * This will recursively fetch configurations up to a certain depth.
     * @param ConfigType The type of configuration to load.
     * @param OptionalPathInRepo Override the default root path from settings.
     * @param MaxRecursionDepth How many levels of subfolders to explore. 0 means only the given path.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Configuration")
    void LoadAllConfigurationsRecursive(EConfigurationType ConfigType, const FString& OptionalPathInRepo = TEXT(""), int32 MaxRecursionDepth = 1);

    /**
     * Gets a list of files and folders at a specific path within the GitHub repository.
     * @param ConfigType The type of configuration being requested.
     * @param PathInRepo The path within the repository to list.
     * @param CompletionDelegate Delegate to call when the list operation completes.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Configuration")
    void GetFileList(EConfigurationType ConfigType, const FString& PathInRepo, FConfigFileListResponseDelegate CompletionDelegate);

    /**
     * Fetches and caches a single configuration file from a specific path within the GitHub repository.
     * @param ConfigType The type of configuration being fetched.
     * @param FilePathInRepo The full path to the configuration file within the repository.
     * @param CompletionDelegate Delegate to call when the fetch operation completes.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Configuration")
    void FetchAndCacheConfig(EConfigurationType ConfigType, const FString& FilePathInRepo, FConfigFileContentResponseDelegate CompletionDelegate);

    /**
     * Gets a parsed configuration struct by its full path name.
     * Assumes the configuration has been loaded and cached.
     * @param ConfigType The type of configuration to retrieve.
     * @param ConfigFilePathInRepo The full path of the configuration file.
     * @param OutStruct The structure to populate with configuration data.
     * @return True if the configuration was found and successfully parsed, false otherwise.
     */
    template<typename TStructType>
    bool GetConfiguration(EConfigurationType ConfigType, const FString& ConfigFilePathInRepo, TStructType& OutStruct) const;

    /**
     * Gets the raw configuration data cache map for a specific configuration type.
     * @param ConfigType The type of configuration to get the cache for.
     * @return A const reference to the internal map of configuration data.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Configuration")
    const TMap<FString, FString>& GetRawConfigCache(EConfigurationType ConfigType) const;

    /**
     * Gets configuration type information including default paths and display names.
     * @param ConfigType The configuration type to get information for.
     * @return Configuration type information structure.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Configuration")
    FConfigurationTypeInfo GetConfigurationTypeInfo(EConfigurationType ConfigType) const;

    /** Delegate broadcast when LoadAllConfigurationsRecursive completes. */
    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|Configuration")
    FOnAllConfigsRequestCompleted OnAllConfigsLoaded;

private:
    // HTTP request handlers
    void OnRecursiveFileListRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, EConfigurationType ConfigType, FString OriginalPath, int32 CurrentDepth, int32 MaxDepth);
    void OnPublicFileListRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FConfigFileListResponseDelegate UserDelegate, FString OriginalPath);
    void OnPublicFileContentRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FConfigFileContentResponseDelegate UserDelegate, FString OriginalFilePath);

    // Local file operations
    void LoadConfigurationsFromLocalRecursive(EConfigurationType ConfigType, const FString& RootDirectory, const FString& CurrentRelativePath, int32 CurrentDepth, int32 MaxDepth);
    bool ReadAndCacheLocalFile(EConfigurationType ConfigType, const FString& FullFilePath, const FString& PathInRepo);

    // Common parsing logic
    template<typename TStructType>
    bool ParseConfigData(const FString& JsonString, const FString& ConfigName, TStructType& OutStruct) const;

    // Configuration type management
    void InitializeConfigurationTypes();
    FString GetCacheKey(EConfigurationType ConfigType, const FString& FilePath) const;

    // Storage for raw JSON data, organized by configuration type
    TMap<EConfigurationType, TMap<FString, FString>> ConfigurationCache;

    // Configuration type information
    TMap<EConfigurationType, FConfigurationTypeInfo> ConfigurationTypes;

    // Helper to get settings
    const UAgentBridgeConfigSettings* GetSettings() const;
};

// Template implementation for GetConfiguration
template<typename TStructType>
bool UConfigurationManager::GetConfiguration(EConfigurationType ConfigType, const FString& ConfigFilePathInRepo, TStructType& OutStruct) const
{
    const TMap<FString, FString>* TypeCache = ConfigurationCache.Find(ConfigType);
    if (!TypeCache)
    {
        UE_LOG(LogTemp, Warning, TEXT("No cache found for configuration type %d"), (int32)ConfigType);
        return false;
    }

    const FString* JsonString = TypeCache->Find(ConfigFilePathInRepo);
    if (JsonString)
    {
        return ParseConfigData(*JsonString, ConfigFilePathInRepo, OutStruct);
    }

    UE_LOG(LogTemp, Warning, TEXT("Configuration '%s' of type %d not found in cache."), *ConfigFilePathInRepo, (int32)ConfigType);
    return false;
}

// Template implementation for ParseConfigData
template<typename TStructType>
bool UConfigurationManager::ParseConfigData(const FString& JsonString, const FString& ConfigName, TStructType& OutStruct) const
{
    if (!FJsonObjectConverter::JsonObjectStringToUStruct(JsonString, &OutStruct, 0, 0))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to parse JSON for config '%s'. JSON: %s"), *ConfigName, *JsonString.Left(500));
        return false;
    }
    return true;
}
